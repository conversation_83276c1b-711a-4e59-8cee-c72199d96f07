{"cmake.sourceDirectory": "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all", "files.associations": {"cmath": "cpp", "cstdint": "cpp", "array": "cpp", "string": "cpp", "string_view": "cpp", "span": "cpp", "vector": "cpp", "bitset": "cpp", "memory": "cpp", "random": "cpp", "future": "cpp", "optional": "cpp", "algorithm": "cpp", "cctype": "cpp", "clocale": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "atomic": "cpp", "hash_map": "cpp", "bit": "cpp", "charconv": "cpp", "chrono": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "exception": "cpp", "functional": "cpp", "iterator": "cpp", "memory_resource": "cpp", "numeric": "cpp", "ratio": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "cinttypes": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "variant": "cpp", "format": "cpp", "__nullptr": "cpp", "*.tcc": "cpp"}}