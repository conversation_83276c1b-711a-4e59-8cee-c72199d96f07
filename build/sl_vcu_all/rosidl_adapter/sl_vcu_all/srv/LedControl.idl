// generated from rosidl_adapter/resource/srv.idl.em
// with input from sl_vcu_all/srv/LedControl.srv
// generated code does not contain a copyright notice

#include "sl_vcu_all/msg/ChannelData.idl"

module sl_vcu_all {
  module srv {
    @verbatim (language="comment", text=
      "LED Display Control Service Definition" "\n"
      "Request")
    struct LedControl_Request {
      @verbatim (language="comment", text=
        "Channel selection - can specify multiple channels," "\n"
        "channels: IDs (0-based indexing), so far up to 2 channels are supported," "\n"
        "sync_channels: If true, all channels only use the first channel_data," "\n"
        "               then sends to hardware simultaneously" "\n"
        "ChannelData structure, refer to ChannelData.msg for details" "\n"
        "each channel_data has the following fields" "\n"
        "##############" "\n"
        "   # Part selection and synchronization" "\n"
        "   uint8[] parts                  # Array of part IDs (0 = whole strip, 1 = first part, 2 = second part)" "\n"
        "   bool sync_parts                # Whether to synchronize parts within this channel" "\n"
        "   # Part data array" "\n"
        "   PartData[] parts_data          # Data for each part (size should match parts array or 1 if sync_parts=true)" "\n"
        "###############" "\n"
        "PartData structure, refer to PartData.msg for details" "\n"
        "##############" "\n"
        "   # Lighting mode (required)" "\n"
        "   string mode                    # \"off\", \"on\", \"breathing\", \"flashing\", \"marquee\"" "\n"
        "   # Color brightness settings (optional, defaults applied if not set)" "\n"
        "   uint8 green_brightness         # Green brightness (0-255)" "\n"
        "   uint8 red_brightness           # Red brightness (0-255)" "\n"
        "   uint8 blue_brightness          # Blue brightness (0-255)" "\n"
        "   # Effect parameters (optional, defaults applied if not set)" "\n"
        "   float32 frequency              # Frequency for breathing/flashing effects (Hz)" "\n"
        "   float32 speed                  # Speed for marquee effect (pixels/second)" "\n"
        "   bool marquee_direction         # Direction for marquee: true=forward, false=reverse" "\n"
        "   float32 on_time_duty           # Duty cycle for flashing effect (0.0-1.0, default 0.5)" "\n"
        "##############" "\n"
        "Array of channel IDs (0-based indexing)")
      sequence<uint8> channels;

      @verbatim (language="comment", text=
        "Whether to synchronize all specified channels")
      boolean sync_channels;

      @verbatim (language="comment", text=
        "Array of channel data, each containing part data")
      sequence<sl_vcu_all::msg::ChannelData> channel_data;
    };
    @verbatim (language="comment", text=
      "Response")
    struct LedControl_Response {
      @verbatim (language="comment", text=
        "Whether the command was executed successfully")
      boolean success;

      @verbatim (language="comment", text=
        "Success or error message")
      string message;

      @verbatim (language="comment", text=
        "Channels that were actually affected")
      sequence<uint8> affected_channels;
    };
  };
};
