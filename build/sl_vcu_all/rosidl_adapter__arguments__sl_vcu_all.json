{"package_name": "sl_vcu_all", "non_idl_tuples": ["/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:srv/AddCanFilter.srv", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:srv/CheckNodeStatus.srv", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:srv/LedControl.srv", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:msg/MotorInfo.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:msg/MotorState.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:msg/BumperState.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:msg/CanFrame.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:msg/BatteryStatus.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:msg/PartData.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:msg/ChannelData.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:msg/JackStatus.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all:action/JackControl.action"]}