{"type_description_msg": {"type_description": {"type_name": "sl_vcu_all/msg/JackStatus", "fields": [{"name": "header", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "std_msgs/msg/Header"}, "default_value": ""}, {"name": "current_stage", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "current_position", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "current_status", "type": {"type_id": 5, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "current_alarm", "type": {"type_id": 5, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": [{"type_name": "builtin_interfaces/msg/Time", "fields": [{"name": "sec", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "nanosec", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "std_msgs/msg/Header", "fields": [{"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}, {"name": "frame_id", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}]}, "type_hashes": [{"type_name": "sl_vcu_all/msg/JackStatus", "hash_string": "RIHS01_516d41b196bd30104bc74bb22d6b9de7b0afc02e0816f2dc32fe86a9c10b9bb0"}, {"type_name": "builtin_interfaces/msg/Time", "hash_string": "RIHS01_b106235e25a4c5ed35098aa0a61a3ee9c9b18d197f398b0e4206cea9acf9c197"}, {"type_name": "std_msgs/msg/Header", "hash_string": "RIHS01_f49fb3ae2cf070f793645ff749683ac6b06203e41c891e17701b1cb597ce6a01"}]}