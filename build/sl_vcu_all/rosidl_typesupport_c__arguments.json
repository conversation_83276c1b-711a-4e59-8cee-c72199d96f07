{"package_name": "sl_vcu_all", "output_dir": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_typesupport_c/sl_vcu_all", "template_dir": "/opt/ros/jazzy/share/rosidl_typesupport_c/resource", "idl_tuples": ["/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:srv/AddCanFilter.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:srv/CheckNodeStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:srv/LedControl.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:msg/MotorInfo.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:msg/MotorState.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:msg/BumperState.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:msg/CanFrame.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:msg/BatteryStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:msg/PartData.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:msg/ChannelData.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:msg/JackStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all:action/JackControl.idl"], "ros_interface_dependencies": ["std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Bool.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Byte.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/ByteMultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Char.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/ColorRGBA.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Empty.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Float32.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Float32MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Float64.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Float64MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Header.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int16.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int16MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int32.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int32MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int64.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int64MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int8.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int8MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/MultiArrayDimension.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/MultiArrayLayout.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/String.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt16.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt16MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt32.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt32MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt64.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt64MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt8.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt8MultiArray.idl", "builtin_interfaces:/opt/ros/jazzy/share/builtin_interfaces/msg/Duration.idl", "builtin_interfaces:/opt/ros/jazzy/share/builtin_interfaces/msg/Time.idl", "service_msgs:/opt/ros/jazzy/share/service_msgs/msg/ServiceEventInfo.idl", "action_msgs:/opt/ros/jazzy/share/action_msgs/msg/GoalInfo.idl", "action_msgs:/opt/ros/jazzy/share/action_msgs/msg/GoalStatus.idl", "action_msgs:/opt/ros/jazzy/share/action_msgs/msg/GoalStatusArray.idl", "action_msgs:/opt/ros/jazzy/share/action_msgs/srv/CancelGoal.idl", "unique_identifier_msgs:/opt/ros/jazzy/share/unique_identifier_msgs/msg/UUID.idl"], "target_dependencies": ["/opt/ros/jazzy/lib/rosidl_typesupport_c/rosidl_typesupport_c", "/opt/ros/jazzy/lib/python3.12/site-packages/rosidl_typesupport_c/__init__.py", "/opt/ros/jazzy/share/rosidl_typesupport_c/resource/action__type_support.c.em", "/opt/ros/jazzy/share/rosidl_typesupport_c/resource/idl__type_support.cpp.em", "/opt/ros/jazzy/share/rosidl_typesupport_c/resource/msg__type_support.cpp.em", "/opt/ros/jazzy/share/rosidl_typesupport_c/resource/srv__type_support.cpp.em", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/srv/AddCanFilter.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/srv/CheckNodeStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/srv/LedControl.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/msg/MotorInfo.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/msg/MotorState.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/msg/BumperState.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/msg/CanFrame.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/msg/BatteryStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/msg/PartData.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/msg/ChannelData.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/msg/JackStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_adapter/sl_vcu_all/action/JackControl.idl", "/opt/ros/jazzy/share/std_msgs/msg/Bool.idl", "/opt/ros/jazzy/share/std_msgs/msg/Byte.idl", "/opt/ros/jazzy/share/std_msgs/msg/ByteMultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Char.idl", "/opt/ros/jazzy/share/std_msgs/msg/ColorRGBA.idl", "/opt/ros/jazzy/share/std_msgs/msg/Empty.idl", "/opt/ros/jazzy/share/std_msgs/msg/Float32.idl", "/opt/ros/jazzy/share/std_msgs/msg/Float32MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Float64.idl", "/opt/ros/jazzy/share/std_msgs/msg/Float64MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Header.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int16.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int16MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int32.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int32MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int64.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int64MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int8.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int8MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/MultiArrayDimension.idl", "/opt/ros/jazzy/share/std_msgs/msg/MultiArrayLayout.idl", "/opt/ros/jazzy/share/std_msgs/msg/String.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt16.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt16MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt32.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt32MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt64.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt64MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt8.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt8MultiArray.idl", "/opt/ros/jazzy/share/builtin_interfaces/msg/Duration.idl", "/opt/ros/jazzy/share/builtin_interfaces/msg/Time.idl", "/opt/ros/jazzy/share/service_msgs/msg/ServiceEventInfo.idl", "/opt/ros/jazzy/share/action_msgs/msg/GoalInfo.idl", "/opt/ros/jazzy/share/action_msgs/msg/GoalStatus.idl", "/opt/ros/jazzy/share/action_msgs/msg/GoalStatusArray.idl", "/opt/ros/jazzy/share/action_msgs/srv/CancelGoal.idl", "/opt/ros/jazzy/share/unique_identifier_msgs/msg/UUID.idl"]}