// generated from rosidl_typesupport_fastrtps_c/resource/idl__rosidl_typesupport_fastrtps_c.h.em
// with input from sl_vcu_all:msg/JackStatus.idl
// generated code does not contain a copyright notice
#ifndef SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
#define SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_


#include <stddef.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "sl_vcu_all/msg/rosidl_typesupport_fastrtps_c__visibility_control.h"
#include "sl_vcu_all/msg/detail/jack_status__struct.h"
#include "fastcdr/Cdr.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_sl_vcu_all
bool cdr_serialize_sl_vcu_all__msg__JackStatus(
  const sl_vcu_all__msg__JackStatus * ros_message,
  eprosima::fastcdr::Cdr & cdr);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_sl_vcu_all
bool cdr_deserialize_sl_vcu_all__msg__JackStatus(
  eprosima::fastcdr::Cdr &,
  sl_vcu_all__msg__JackStatus * ros_message);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_sl_vcu_all
size_t get_serialized_size_sl_vcu_all__msg__JackStatus(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_sl_vcu_all
size_t max_serialized_size_sl_vcu_all__msg__JackStatus(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_sl_vcu_all
bool cdr_serialize_key_sl_vcu_all__msg__JackStatus(
  const sl_vcu_all__msg__JackStatus * ros_message,
  eprosima::fastcdr::Cdr & cdr);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_sl_vcu_all
size_t get_serialized_size_key_sl_vcu_all__msg__JackStatus(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_sl_vcu_all
size_t max_serialized_size_key_sl_vcu_all__msg__JackStatus(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_sl_vcu_all
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, sl_vcu_all, msg, JackStatus)();

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
