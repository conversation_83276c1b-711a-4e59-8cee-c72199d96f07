// generated from rosidl_typesupport_fastrtps_cpp/resource/idl__rosidl_typesupport_fastrtps_cpp.hpp.em
// with input from sl_vcu_all:msg/JackStatus.idl
// generated code does not contain a copyright notice

#ifndef SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_FASTRTPS_CPP_HPP_
#define SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_FASTRTPS_CPP_HPP_

#include <cstddef>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "sl_vcu_all/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h"
#include "sl_vcu_all/msg/detail/jack_status__struct.hpp"

#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-parameter"
# ifdef __clang__
#  pragma clang diagnostic ignored "-Wdeprecated-register"
#  pragma clang diagnostic ignored "-Wreturn-type-c-linkage"
# endif
#endif
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif

#include "fastcdr/Cdr.h"

namespace sl_vcu_all
{

namespace msg
{

namespace typesupport_fastrtps_cpp
{

bool
ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_sl_vcu_all
cdr_serialize(
  const sl_vcu_all::msg::JackStatus & ros_message,
  eprosima::fastcdr::Cdr & cdr);

bool
ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_sl_vcu_all
cdr_deserialize(
  eprosima::fastcdr::Cdr & cdr,
  sl_vcu_all::msg::JackStatus & ros_message);

size_t
ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_sl_vcu_all
get_serialized_size(
  const sl_vcu_all::msg::JackStatus & ros_message,
  size_t current_alignment);

size_t
ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_sl_vcu_all
max_serialized_size_JackStatus(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

bool
ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_sl_vcu_all
cdr_serialize_key(
  const sl_vcu_all::msg::JackStatus & ros_message,
  eprosima::fastcdr::Cdr &);

size_t
ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_sl_vcu_all
get_serialized_size_key(
  const sl_vcu_all::msg::JackStatus & ros_message,
  size_t current_alignment);

size_t
ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_sl_vcu_all
max_serialized_size_key_JackStatus(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

}  // namespace typesupport_fastrtps_cpp

}  // namespace msg

}  // namespace sl_vcu_all

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_sl_vcu_all
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_cpp, sl_vcu_all, msg, JackStatus)();

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_FASTRTPS_CPP_HPP_
