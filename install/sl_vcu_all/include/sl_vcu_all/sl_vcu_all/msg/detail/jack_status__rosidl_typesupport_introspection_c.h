// generated from rosidl_typesupport_introspection_c/resource/idl__rosidl_typesupport_introspection_c.h.em
// with input from sl_vcu_all:msg/JackStatus.idl
// generated code does not contain a copyright notice

#ifndef SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_INTROSPECTION_C_H_
#define SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_INTROSPECTION_C_H_

#ifdef __cplusplus
extern "C"
{
#endif


#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "sl_vcu_all/msg/rosidl_typesupport_introspection_c__visibility_control.h"

ROSIDL_TYPESUPPORT_INTROSPECTION_C_PUBLIC_sl_vcu_all
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, sl_vcu_all, msg, JackStatus)();

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__ROSIDL_TYPESUPPORT_INTROSPECTION_C_H_
