// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from sl_vcu_all:msg/JackStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/jack_status.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__STRUCT_HPP_
#define SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__msg__JackStatus __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__msg__JackStatus __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct JackStatus_
{
  using Type = JackStatus_<ContainerAllocator>;

  explicit JackStatus_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->current_stage = "";
      this->current_position = 0l;
      this->current_status = 0;
      this->current_alarm = 0;
    }
  }

  explicit JackStatus_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init),
    current_stage(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->current_stage = "";
      this->current_position = 0l;
      this->current_status = 0;
      this->current_alarm = 0;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _current_stage_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _current_stage_type current_stage;
  using _current_position_type =
    int32_t;
  _current_position_type current_position;
  using _current_status_type =
    uint16_t;
  _current_status_type current_status;
  using _current_alarm_type =
    uint16_t;
  _current_alarm_type current_alarm;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__current_stage(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->current_stage = _arg;
    return *this;
  }
  Type & set__current_position(
    const int32_t & _arg)
  {
    this->current_position = _arg;
    return *this;
  }
  Type & set__current_status(
    const uint16_t & _arg)
  {
    this->current_status = _arg;
    return *this;
  }
  Type & set__current_alarm(
    const uint16_t & _arg)
  {
    this->current_alarm = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::msg::JackStatus_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::msg::JackStatus_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::msg::JackStatus_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::msg::JackStatus_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::JackStatus_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::JackStatus_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::JackStatus_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::JackStatus_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::msg::JackStatus_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::msg::JackStatus_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__msg__JackStatus
    std::shared_ptr<sl_vcu_all::msg::JackStatus_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__msg__JackStatus
    std::shared_ptr<sl_vcu_all::msg::JackStatus_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackStatus_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->current_stage != other.current_stage) {
      return false;
    }
    if (this->current_position != other.current_position) {
      return false;
    }
    if (this->current_status != other.current_status) {
      return false;
    }
    if (this->current_alarm != other.current_alarm) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackStatus_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackStatus_

// alias to use template instance with default allocator
using JackStatus =
  sl_vcu_all::msg::JackStatus_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__STRUCT_HPP_
