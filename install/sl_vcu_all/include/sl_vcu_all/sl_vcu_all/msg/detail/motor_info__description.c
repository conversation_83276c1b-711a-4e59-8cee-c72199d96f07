// generated from rosidl_generator_c/resource/idl__description.c.em
// with input from sl_vcu_all:msg/MotorInfo.idl
// generated code does not contain a copyright notice

#include "sl_vcu_all/msg/detail/motor_info__functions.h"

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__msg__MotorInfo__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x36, 0xa7, 0x00, 0x85, 0xfb, 0x71, 0xb1, 0x28,
      0x52, 0xae, 0xcb, 0x1e, 0x6b, 0x1a, 0x9e, 0x94,
      0xed, 0x7a, 0xd2, 0xac, 0xa9, 0xf3, 0x51, 0x3d,
      0x46, 0x9b, 0x2c, 0x4d, 0xc3, 0xed, 0x7c, 0x23,
    }};
  return &hash;
}

#include <assert.h>
#include <string.h>

// Include directives for referenced types
#include "std_msgs/msg/detail/header__functions.h"
#include "builtin_interfaces/msg/detail/time__functions.h"

// Hashes for external referenced types
#ifndef NDEBUG
static const rosidl_type_hash_t builtin_interfaces__msg__Time__EXPECTED_HASH = {1, {
    0xb1, 0x06, 0x23, 0x5e, 0x25, 0xa4, 0xc5, 0xed,
    0x35, 0x09, 0x8a, 0xa0, 0xa6, 0x1a, 0x3e, 0xe9,
    0xc9, 0xb1, 0x8d, 0x19, 0x7f, 0x39, 0x8b, 0x0e,
    0x42, 0x06, 0xce, 0xa9, 0xac, 0xf9, 0xc1, 0x97,
  }};
static const rosidl_type_hash_t std_msgs__msg__Header__EXPECTED_HASH = {1, {
    0xf4, 0x9f, 0xb3, 0xae, 0x2c, 0xf0, 0x70, 0xf7,
    0x93, 0x64, 0x5f, 0xf7, 0x49, 0x68, 0x3a, 0xc6,
    0xb0, 0x62, 0x03, 0xe4, 0x1c, 0x89, 0x1e, 0x17,
    0x70, 0x1b, 0x1c, 0xb5, 0x97, 0xce, 0x6a, 0x01,
  }};
#endif

static char sl_vcu_all__msg__MotorInfo__TYPE_NAME[] = "sl_vcu_all/msg/MotorInfo";
static char builtin_interfaces__msg__Time__TYPE_NAME[] = "builtin_interfaces/msg/Time";
static char std_msgs__msg__Header__TYPE_NAME[] = "std_msgs/msg/Header";

// Define type names, field names, and default values
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__header[] = "header";
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__left_current[] = "left_current";
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__right_current[] = "right_current";
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__left_temp[] = "left_temp";
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__right_temp[] = "right_temp";
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__driver_temp[] = "driver_temp";
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__left_pos_encoder[] = "left_pos_encoder";
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__right_pos_encoder[] = "right_pos_encoder";
static char sl_vcu_all__msg__MotorInfo__FIELD_NAME__alarm_code[] = "alarm_code";

static rosidl_runtime_c__type_description__Field sl_vcu_all__msg__MotorInfo__FIELDS[] = {
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__header, 6, 6},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {std_msgs__msg__Header__TYPE_NAME, 19, 19},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__left_current, 12, 12},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_DOUBLE,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__right_current, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_DOUBLE,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__left_temp, 9, 9},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_DOUBLE,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__right_temp, 10, 10},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_DOUBLE,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__driver_temp, 11, 11},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_DOUBLE,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__left_pos_encoder, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_INT32,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__right_pos_encoder, 17, 17},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_INT32,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__MotorInfo__FIELD_NAME__alarm_code, 10, 10},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT32,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__msg__MotorInfo__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {std_msgs__msg__Header__TYPE_NAME, 19, 19},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__msg__MotorInfo__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__msg__MotorInfo__TYPE_NAME, 24, 24},
      {sl_vcu_all__msg__MotorInfo__FIELDS, 9, 9},
    },
    {sl_vcu_all__msg__MotorInfo__REFERENCED_TYPE_DESCRIPTIONS, 2, 2},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&std_msgs__msg__Header__EXPECTED_HASH, std_msgs__msg__Header__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = std_msgs__msg__Header__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}

static char toplevel_type_raw_source[] =
  "std_msgs/Header header\n"
  "float64 left_current\n"
  "float64 right_current\n"
  "float64 left_temp\n"
  "float64 right_temp\n"
  "float64 driver_temp\n"
  "int32 left_pos_encoder\n"
  "int32 right_pos_encoder\n"
  "uint32 alarm_code";

static char msg_encoding[] = "msg";

// Define all individual source functions

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__msg__MotorInfo__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__msg__MotorInfo__TYPE_NAME, 24, 24},
    {msg_encoding, 3, 3},
    {toplevel_type_raw_source, 187, 187},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__msg__MotorInfo__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[3];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 3, 3};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__msg__MotorInfo__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *std_msgs__msg__Header__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}
