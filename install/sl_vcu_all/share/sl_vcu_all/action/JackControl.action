# Jack Control Action Definition
# This action provides control over the lifting jack system

# Goal - The requested jack operation
string command  # One of: "detect_base", "lift_up", "lift_down", "stop", "clear_alarm"
uint32 target_position  # Target position for lift operations (optional, used for lift_up/lift_down)
uint32 speed  # Speed for lift operations (optional, RPM)

---

# Result - The final result of the jack operation
bool success  # Whether the operation completed successfully
string message  # Human-readable result message
uint32 final_position  # Final position after operation
uint16 final_status  # Final status word from jack controller
uint16 alarm_code  # Any alarm codes present

---

# Feedback - Ongoing feedback during the operation
string current_stage  # Current stage: "init", "detecting_base", "base_stop", "lifting_up", "lifting_down", "top_stop", "middle_stop"
uint32 current_position  # Current position
uint16 current_status  # Current status word
uint16 current_alarm  # Current alarm code
float32 progress  # Progress percentage (0.0 to 100.0)
