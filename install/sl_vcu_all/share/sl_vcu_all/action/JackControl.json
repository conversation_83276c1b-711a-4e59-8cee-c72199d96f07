{"type_description_msg": {"type_description": {"type_name": "sl_vcu_all/action/JackControl", "fields": [{"name": "goal", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_Goal"}, "default_value": ""}, {"name": "result", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_Result"}, "default_value": ""}, {"name": "feedback", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_Feedback"}, "default_value": ""}, {"name": "send_goal_service", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_SendGoal"}, "default_value": ""}, {"name": "get_result_service", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_GetResult"}, "default_value": ""}, {"name": "feedback_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_FeedbackMessage"}, "default_value": ""}]}, "referenced_type_descriptions": [{"type_name": "builtin_interfaces/msg/Time", "fields": [{"name": "sec", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "nanosec", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "service_msgs/msg/ServiceEventInfo", "fields": [{"name": "event_type", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}, {"name": "client_gid", "type": {"type_id": 51, "capacity": 16, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sequence_number", "type": {"type_id": 8, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_Feedback", "fields": [{"name": "current_stage", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "current_position", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "current_status", "type": {"type_id": 5, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "current_alarm", "type": {"type_id": 5, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "progress", "type": {"type_id": 10, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_FeedbackMessage", "fields": [{"name": "goal_id", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "unique_identifier_msgs/msg/UUID"}, "default_value": ""}, {"name": "feedback", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_Feedback"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_GetResult", "fields": [{"name": "request_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_GetResult_Request"}, "default_value": ""}, {"name": "response_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_GetResult_Response"}, "default_value": ""}, {"name": "event_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_GetResult_Event"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_GetResult_Event", "fields": [{"name": "info", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "service_msgs/msg/ServiceEventInfo"}, "default_value": ""}, {"name": "request", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_GetResult_Request"}, "default_value": ""}, {"name": "response", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_GetResult_Response"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_GetResult_Request", "fields": [{"name": "goal_id", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "unique_identifier_msgs/msg/UUID"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_GetResult_Response", "fields": [{"name": "status", "type": {"type_id": 2, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "result", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_Result"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_Goal", "fields": [{"name": "command", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "target_position", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "speed", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_Result", "fields": [{"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "final_position", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "final_status", "type": {"type_id": 5, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "alarm_code", "type": {"type_id": 5, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_SendGoal", "fields": [{"name": "request_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_SendGoal_Request"}, "default_value": ""}, {"name": "response_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_SendGoal_Response"}, "default_value": ""}, {"name": "event_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_SendGoal_Event"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_SendGoal_Event", "fields": [{"name": "info", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "service_msgs/msg/ServiceEventInfo"}, "default_value": ""}, {"name": "request", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_SendGoal_Request"}, "default_value": ""}, {"name": "response", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_SendGoal_Response"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_SendGoal_Request", "fields": [{"name": "goal_id", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "unique_identifier_msgs/msg/UUID"}, "default_value": ""}, {"name": "goal", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/action/JackControl_Goal"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/action/JackControl_SendGoal_Response", "fields": [{"name": "accepted", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}]}, {"type_name": "unique_identifier_msgs/msg/UUID", "fields": [{"name": "uuid", "type": {"type_id": 51, "capacity": 16, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}]}, "type_hashes": [{"type_name": "sl_vcu_all/action/JackControl", "hash_string": "RIHS01_ace7b743b61d70805ed006aaa92040eb5586f5695bab74821f3ed45c5bec9933"}, {"type_name": "builtin_interfaces/msg/Time", "hash_string": "RIHS01_b106235e25a4c5ed35098aa0a61a3ee9c9b18d197f398b0e4206cea9acf9c197"}, {"type_name": "service_msgs/msg/ServiceEventInfo", "hash_string": "RIHS01_41bcbbe07a75c9b52bc96bfd5c24d7f0fc0a08c0cb7921b3373c5732345a6f45"}, {"type_name": "sl_vcu_all/action/JackControl_Feedback", "hash_string": "RIHS01_de20c64e8f2a2aba94cb3709e2cbf5d4b173afae55013117e70c6ef8b12ff918"}, {"type_name": "sl_vcu_all/action/JackControl_FeedbackMessage", "hash_string": "RIHS01_da711ee6a259ef68d0b5d898094257204074640c8a043c78c89eb3d3b8e2a7ae"}, {"type_name": "sl_vcu_all/action/JackControl_GetResult", "hash_string": "RIHS01_228bc1691cfec5499629f0b554bffe8920fb12507f601f387b86d1c5e2eb72e5"}, {"type_name": "sl_vcu_all/action/JackControl_GetResult_Event", "hash_string": "RIHS01_b0c588820d28240166e5c1f5d377ac976ec55464d9371a95d7f4a6075c9a75cf"}, {"type_name": "sl_vcu_all/action/JackControl_GetResult_Request", "hash_string": "RIHS01_664eff603dc52e29957506fcbde11fe4bbb96e4e44f2a36815e6f641b8f1814a"}, {"type_name": "sl_vcu_all/action/JackControl_GetResult_Response", "hash_string": "RIHS01_a17a4c166cf7396b4bf3c8155796868902f6c93f251b214541ffaa2e17c5e667"}, {"type_name": "sl_vcu_all/action/JackControl_Goal", "hash_string": "RIHS01_a31827370a9a16e7fb7b52968d7479019efd858897b9936f187656b3b4b5bb74"}, {"type_name": "sl_vcu_all/action/JackControl_Result", "hash_string": "RIHS01_3ecab73d46e96143f74cf183caf4de1f44c9349ee45c7bb277d5f04fe0a0a3dd"}, {"type_name": "sl_vcu_all/action/JackControl_SendGoal", "hash_string": "RIHS01_10e9d26c94899c7987ba23bbc5d027c2c9407282ea8a058783e7326b3d81a2b6"}, {"type_name": "sl_vcu_all/action/JackControl_SendGoal_Event", "hash_string": "RIHS01_b069de5ea6bcada70d62b731b1ff5cea85d7bb1a3a909d8ddf744ca0559211fe"}, {"type_name": "sl_vcu_all/action/JackControl_SendGoal_Request", "hash_string": "RIHS01_859c79fd78ea4f78cb7522a50b9c7c656f7c59f04d07d33a51c15ed0d2383e54"}, {"type_name": "sl_vcu_all/action/JackControl_SendGoal_Response", "hash_string": "RIHS01_65df3f9fdc98361153b7d6cbe69e6faf385567ffa63972d2457f00afafd89798"}, {"type_name": "unique_identifier_msgs/msg/UUID", "hash_string": "RIHS01_1b8e8aca958cbea28fe6ef60bf6c19b683c97a9ef60bb34752067d0f2f7ab437"}]}