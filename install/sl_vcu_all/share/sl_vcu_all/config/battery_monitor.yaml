battery_monitor:
  ros__parameters:
    # CAN communication parameters
    can_interface: "can0"
    can_id_battery_info: 0x100
    can_id_battery_status: 0x101
    min_send_interval_ms: 20
    request_interval_ms: 1000

    # GPIO parameters
    auto_charging_gpio_path: "/sys/class/gpio/gpio39/value"
    manual_charging_gpio_path: "/sys/class/gpio/gpio27/value"
    auto_charging_trigger_value: 0
    manual_charging_trigger_value: 0
    gpio_check_period_ms: 10
    gpio_debounce_time_ms: 10

    # Topic and publishing parameters
    battery_topic: "battery_state"
    battery_status_topic: "battery_status"
    publish_rate_ms: 1000
    battery_status_publish_rate_ms: 20 