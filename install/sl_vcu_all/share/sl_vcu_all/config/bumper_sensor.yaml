bumper_sensor:
  ros__parameters:
    # GPIO configuration
    front_bumper_gpio_path: "/sys/class/gpio/gpio156/value"  # GPIO file path for front bumper
    back_bumper_gpio_path: "/sys/class/gpio/gpio157/value"   # GPIO file path for back bumper
    front_bumper_trigger_value: 0                            # GPIO value when front bumper is triggered
    back_bumper_trigger_value: 0                             # GPIO value when back bumper is triggered
    gpio_check_period_ms: 10                                 # GPIO check period in milliseconds
    gpio_debounce_time_ms: 10                                # GPIO debounce time in milliseconds

    # Publishing configuration
    publish_rate_ms: 20                     # Rate to publish bumper state (milliseconds)
    bumper_topic: "bumper_state"           # Topic name for bumper state messages

    # Disable functionality
    disable_timeout_ms: 1000               # Timeout for bumper disable function (milliseconds)
