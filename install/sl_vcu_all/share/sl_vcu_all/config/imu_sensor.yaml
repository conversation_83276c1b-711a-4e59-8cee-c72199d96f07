# IMU Sensor Configuration
# This file contains all configurable parameters for the IMU Sensor node

imu_sensor:
  ros__parameters:
    # Basic IMU device parameters
    gyro_device_path: "/dev/iio:device1"          # Gyroscope device path (device1)
    accel_device_path: "/dev/iio:device2"         # Accelerometer device path (device2)
    imu_frame_id: "imu_link"                      # Frame ID for IMU data
    publish_period_ms: 10                          # Publishing rate in milliseconds
    imu_topic: "sl_vcu_all/imu_data_raw"                         # IMU data topic name

    # IMU sensor parameters
    poll_timeout: 1000                            # Poll timeout in milliseconds
    imu_sensor_acc_sensitivity: 4                 # Accelerometer sensitivity in g
    imu_sensor_gyro_sensitivity: 2000             # Gyroscope sensitivity in dps
    # imu_sensor_gyro_sensitivity: 500
    timestamp_sync_tolerance_ns: 10000000         # Timestamp synchronization tolerance in nanoseconds (5ms)

    # Bias estimation and filtering parameters
    initial_bias_offset: 0.1                      # Initial large offset for gyro bias filtering (rad/s)
    bias_calculation_time: 10.0                   # Time to calculate initial bias in seconds
    bias_update_time: 5.0                         # Time window for bias update analysis in seconds
    bias_update_threshold: 0.01                   # Standard deviation threshold for bias update (rad/s)
    cmd_vel_timeout: 2.0                          # Timeout for cmd_vel messages in seconds

    # Topic names
    cmd_vel_topic: "cmd_vel"                      # Command velocity topic name
    imu_filtered_topic: "sl_vcu_all/imu_data_filtered"  # Filtered IMU data topic name

    # TF publishing parameters
    publish_tf: false                             # Whether to publish TF transforms
    parent_frame_id: "base_link"                  # Parent frame for TF (e.g., "base_link", "odom")
    child_frame_id: "imu_link"                    # Child frame for TF (should match imu_frame_id)

    # Note: Device configuration (sampling rates, scales, buffer settings) is handled
    # by the setup script: sudo ./scripts/setup_imu_devices.sh
