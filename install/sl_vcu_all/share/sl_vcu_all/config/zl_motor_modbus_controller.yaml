# ZL Motor Modbus Controller Configuration
# This file contains all configurable parameters for the ZLMotorModbusController node

zl_motor_modbus_controller:
  ros__parameters:
    # Modbus RTU communication parameters
    serial_device: "/dev/ttyUSB0"       # Serial device path for RS485 communication
    baud_rate: 115200                   # Baud rate for serial communication
    parity: "N"                         # Parity: N (None), E (Even), O (Odd)
    data_bits: 8                        # Number of data bits
    stop_bits: 1                        # Number of stop bits
    slave_address: 1                    # Modbus slave address (default 1, configurable)

    # Robot physical parameters
    wheel_diameter_left: 0.1388         # Left wheel diameter in meters
    wheel_diameter_right: 0.140         # Right wheel diameter in meters
    wheel_separation: 0.390             # Distance between wheels in meters
    gear_ratio: 1.0                     # Motor gear ratio
    encoder_resolution: 16384.0         # Encoder ticks per revolution

    # Frame IDs for TF and odometry
    odom_frame_id: "odom"              # Odometry frame ID
    base_frame_id: "base_link"         # Base frame ID
    publish_tf: true                   # Whether to publish TF transforms

    # Topic names
    cmd_vel_topic: "cmd_vel"           # Command velocity topic
    odom_topic: "odom"                 # Odometry topic
    motor_info_topic: "motor_info"     # Motor information topic
    motor_state_topic: "motor_state"   # Motor state topic
    bumper_topic: "bumper_state"       # Bumper state topic

    # Control timing parameters
    control_cycle_ms: 25               # Main control loop cycle time in milliseconds
    print_status_out: false            # Whether to print status information
    status_update_cycle_ms: 1000       # Status update cycle for temperatures and currents in milliseconds

    # Timeout parameters
    cmd_vel_timeout_ms: 500            # Command velocity timeout in milliseconds
    bumper_timeout_ms: 5000            # Bumper state timeout in milliseconds
    response_timeout_ms: 100          # Response timeout in milliseconds

    # Publishing options
    publish_motor_info: true          # Whether to publish motor info messages

    # GPIO parameters
    emergency_stop_bit0_trigger_level: 0  # Emergency stop bit0 trigger level: 0 = low level triggers emergency stop, 1 = high level triggers emergency stop
