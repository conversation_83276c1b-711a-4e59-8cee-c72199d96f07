from launch import LaunchDescription
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument
from ament_index_python.packages import get_package_share_directory
import os

def generate_launch_description():
    # Get the package share directory
    pkg_share = get_package_share_directory('sl_vcu_all')

    # Declare launch arguments
    can_interface_arg = DeclareLaunchArgument(
        'can_interface',
        default_value='can0',
        description='CAN interface name'
    )

    input_device_arg = DeclareLaunchArgument(
        'input_device_path',
        default_value='/dev/input/event2',
        description='Path to input device for charging status'
    )

    # Create the node
    battery_monitor_node = Node(
        package='sl_vcu_all',
        executable='battery_monitor_node',
        name='battery_monitor',
        output='screen',
        parameters=[
            os.path.join(pkg_share, 'config', 'battery_monitor.yaml'),
            {
                'can_interface': LaunchConfiguration('can_interface'),
                'input_device_path': LaunchConfiguration('input_device_path')
            }
        ]
    )

    return LaunchDescription([
        can_interface_arg,
        input_device_arg,
        battery_monitor_node
    ]) 