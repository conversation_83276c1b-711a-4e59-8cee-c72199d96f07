#!/usr/bin/env python3

"""
Launch file for ZL Motor Modbus Controller Node

This launch file starts the ZL Motor Modbus Controller node with configurable parameters.
It supports different configurations for production, simulation, and debugging.

Usage:
  ros2 launch sl_vcu_all zl_motor_modbus_controller.launch.py
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # Declare launch arguments
    serial_device_arg = DeclareLaunchArgument(
        'serial_device',
        default_value='',
        description='Override serial device path (e.g., /dev/ttyUSB0)'
    )
    
    baud_rate_arg = DeclareLaunchArgument(
        'baud_rate',
        default_value='',
        description='Override baud rate (e.g., 115200)'
    )
    
    slave_address_arg = DeclareLaunchArgument(
        'slave_address',
        default_value='',
        description='Override Modbus slave address (e.g., 1)'
    )

    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Log level: debug, info, warn, error'
    )

    def launch_setup(context, *args, **kwargs):
        # Get launch configuration values
        serial_device = LaunchConfiguration('serial_device').perform(context)
        baud_rate = LaunchConfiguration('baud_rate').perform(context)
        slave_address = LaunchConfiguration('slave_address').perform(context)
        log_level = LaunchConfiguration('log_level').perform(context)
        
        # Get package share directory
        package_share_dir = get_package_share_directory('sl_vcu_all')
        
        # Determine configuration file based on config argument
        config_file = os.path.join(package_share_dir, 'config', 'zl_motor_modbus_controller.yaml')       
        
        # Build parameters dictionary
        parameters = [config_file]
        
        # Add parameter overrides if provided
        param_overrides = {}
        if serial_device:
            param_overrides['serial_device'] = serial_device
        if baud_rate:
            param_overrides['baud_rate'] = int(baud_rate)
        if slave_address:
            param_overrides['slave_address'] = int(slave_address)
        
        if param_overrides:
            parameters.append(param_overrides)
        
        # Create the node
        zl_motor_modbus_controller_node = Node(
            package='sl_vcu_all',
            executable='zl_motor_modbus_controller_node',
            name='zl_motor_modbus_controller',
            parameters=parameters,
            output='screen',
            arguments=['--ros-args', '--log-level', log_level],
        )
        
        return [zl_motor_modbus_controller_node]

    return LaunchDescription([
        serial_device_arg,
        baud_rate_arg,
        slave_address_arg,
        log_level_arg,
        OpaqueFunction(function=launch_setup)
    ])


if __name__ == '__main__':
    generate_launch_description()
