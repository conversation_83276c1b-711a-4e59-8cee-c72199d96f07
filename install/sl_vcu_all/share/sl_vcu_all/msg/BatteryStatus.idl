// generated from rosidl_adapter/resource/msg.idl.em
// with input from sl_vcu_all/msg/BatteryStatus.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sl_vcu_all {
  module msg {
    @verbatim (language="comment", text=
      "Battery status message")
    struct BatteryStatus {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Battery states" "\n"
        "Whether the battery is being charged automatically")
      boolean is_auto_charging;

      @verbatim (language="comment", text=
        "Whether the battery is being charged manually")
      boolean is_manual_charging;

      @verbatim (language="comment", text=
        "Whether the battery is discharging")
      boolean is_discharging;

      @verbatim (language="comment", text=
        "Remaining battery percentage (0-100)")
      float remaining_percent;
    };
  };
};
