// generated from rosidl_adapter/resource/msg.idl.em
// with input from sl_vcu_all/msg/BumperState.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sl_vcu_all {
  module msg {
    struct BumperState {
      std_msgs::msg::Header header;

      boolean front_bumper_triggered;

      boolean back_bumper_triggered;

      @verbatim (language="comment", text=
        "Combined status for additional info")
      uint32 bumper_status;
    };
  };
};
