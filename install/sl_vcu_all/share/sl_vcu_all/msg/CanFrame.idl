// generated from rosidl_adapter/resource/msg.idl.em
// with input from sl_vcu_all/msg/CanFrame.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sl_vcu_all {
  module msg {
    typedef uint8 uint8__8[8];
    @verbatim (language="comment", text=
      "CAN Frame message definition" "\n"
      "This message represents a single CAN frame")
    struct CanFrame {
      std_msgs::msg::Header header;

      uint32 id;

      boolean is_rtr;

      boolean is_extended;

      boolean is_error;

      uint8 dlc;

      uint8__8 data;
    };
  };
};
