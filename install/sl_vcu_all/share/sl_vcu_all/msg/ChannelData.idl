// generated from rosidl_adapter/resource/msg.idl.em
// with input from sl_vcu_all/msg/ChannelData.msg
// generated code does not contain a copyright notice

#include "sl_vcu_all/msg/PartData.idl"

module sl_vcu_all {
  module msg {
    @verbatim (language="comment", text=
      "Channel Data for LED Control" "\n"
      "This message defines the control data for a single LED strip channel")
    struct ChannelData {
      @verbatim (language="comment", text=
        "Part selection and synchronization" "\n"
        "Array of part IDs (0 = whole strip, 1 = first part, 2 = second part)")
      sequence<uint8> parts;

      @verbatim (language="comment", text=
        "Whether to synchronize parts within this channel")
      boolean sync_parts;

      @verbatim (language="comment", text=
        "Part data array" "\n"
        "Data for each part (size should match parts array or 1 if sync_parts=true)")
      sequence<sl_vcu_all::msg::PartData> parts_data;
    };
  };
};
