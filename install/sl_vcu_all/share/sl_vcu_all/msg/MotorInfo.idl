// generated from rosidl_adapter/resource/msg.idl.em
// with input from sl_vcu_all/msg/MotorInfo.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sl_vcu_all {
  module msg {
    struct MotorInfo {
      std_msgs::msg::Header header;

      double left_current;

      double right_current;

      double left_temp;

      double right_temp;

      double driver_temp;

      int32 left_pos_encoder;

      int32 right_pos_encoder;

      uint32 alarm_code;
    };
  };
};
