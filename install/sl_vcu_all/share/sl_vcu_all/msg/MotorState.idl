// generated from rosidl_adapter/resource/msg.idl.em
// with input from sl_vcu_all/msg/MotorState.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sl_vcu_all {
  module msg {
    @verbatim (language="comment", text=
      "Motor state message")
    struct MotorState {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Brake and emergency status" "\n"
        "true if brake is released")
      boolean brake_release;

      @verbatim (language="comment", text=
        "true if emergency stop is triggered")
      boolean emergency_stop;

      @verbatim (language="comment", text=
        "Motor state" "\n"
        "\"running\", \"error\"")
      string state;

      @verbatim (language="comment", text=
        "Motor alarm/error code")
      int32 error_code;

      @verbatim (language="comment", text=
        "Human readable error information")
      string error_info;
    };
  };
};
