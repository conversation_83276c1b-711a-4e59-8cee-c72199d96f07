// generated from rosidl_adapter/resource/msg.idl.em
// with input from sl_vcu_all/msg/PartData.msg
// generated code does not contain a copyright notice


module sl_vcu_all {
  module msg {
    @verbatim (language="comment", text=
      "Part Data for LED Control" "\n"
      "This message defines the control data for a single part of an LED strip")
    struct PartData {
      @verbatim (language="comment", text=
        "Lighting mode (required)" "\n"
        "\"off\", \"on\", \"breathing\", \"flashing\", \"marquee\"")
      string mode;

      @verbatim (language="comment", text=
        "Color brightness settings (optional, defaults applied if not set)" "\n"
        "Green brightness (0-255)")
      uint8 green_brightness;

      @verbatim (language="comment", text=
        "Red brightness (0-255)")
      uint8 red_brightness;

      @verbatim (language="comment", text=
        "Blue brightness (0-255)")
      uint8 blue_brightness;

      @verbatim (language="comment", text=
        "Effect parameters (optional, defaults applied if not set)" "\n"
        "Frequency for breathing/flashing effects (Hz)")
      float frequency;

      @verbatim (language="comment", text=
        "Speed for marquee effect (pixels/second)")
      float speed;

      @verbatim (language="comment", text=
        "Direction for marquee: true=forward, false=reverse")
      boolean marquee_direction;

      @verbatim (language="comment", text=
        "Duty cycle for flashing effect (0.0-1.0, default 0.5)")
      float on_time_duty;
    };
  };
};
