{"type_description_msg": {"type_description": {"type_name": "sl_vcu_all/msg/PartData", "fields": [{"name": "mode", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "green_brightness", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "red_brightness", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "blue_brightness", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "frequency", "type": {"type_id": 10, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "speed", "type": {"type_id": 10, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "marquee_direction", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "on_time_duty", "type": {"type_id": 10, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "sl_vcu_all/msg/PartData", "hash_string": "RIHS01_44e09f9f38472e212fec22e7182c212d97ba1b6501c50e89fb31199e908f32c3"}]}