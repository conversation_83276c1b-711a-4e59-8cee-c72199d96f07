// generated from rosidl_adapter/resource/srv.idl.em
// with input from sl_vcu_all/srv/AddCanFilter.srv
// generated code does not contain a copyright notice


module sl_vcu_all {
  module srv {
    @verbatim (language="comment", text=
      "Request")
    struct AddCanFilter_Request {
      @verbatim (language="comment", text=
        "CAN ID to filter")
      uint32 can_id;

      @verbatim (language="comment", text=
        "Topic name to publish messages for this CAN ID")
      string topic_name;
    };
    @verbatim (language="comment", text=
      "Response")
    struct AddCanFilter_Response {
      @verbatim (language="comment", text=
        "Whether the filter was added successfully")
      boolean success;

      @verbatim (language="comment", text=
        "Success or error message")
      string message;
    };
  };
};
