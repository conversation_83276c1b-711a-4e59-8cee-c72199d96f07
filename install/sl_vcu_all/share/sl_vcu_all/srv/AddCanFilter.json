{"type_description_msg": {"type_description": {"type_name": "sl_vcu_all/srv/AddCanFilter", "fields": [{"name": "request_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/AddCanFilter_Request"}, "default_value": ""}, {"name": "response_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/AddCanFilter_Response"}, "default_value": ""}, {"name": "event_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/AddCanFilter_Event"}, "default_value": ""}]}, "referenced_type_descriptions": [{"type_name": "builtin_interfaces/msg/Time", "fields": [{"name": "sec", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "nanosec", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "service_msgs/msg/ServiceEventInfo", "fields": [{"name": "event_type", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}, {"name": "client_gid", "type": {"type_id": 51, "capacity": 16, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sequence_number", "type": {"type_id": 8, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/AddCanFilter_Event", "fields": [{"name": "info", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "service_msgs/msg/ServiceEventInfo"}, "default_value": ""}, {"name": "request", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/AddCanFilter_Request"}, "default_value": ""}, {"name": "response", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/AddCanFilter_Response"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/AddCanFilter_Request", "fields": [{"name": "can_id", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "topic_name", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/AddCanFilter_Response", "fields": [{"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}]}, "type_hashes": [{"type_name": "sl_vcu_all/srv/AddCanFilter", "hash_string": "RIHS01_adb1468e507b6f3d8b93fa53e302bae3838c959929d18add5ce95026b9e2668c"}, {"type_name": "builtin_interfaces/msg/Time", "hash_string": "RIHS01_b106235e25a4c5ed35098aa0a61a3ee9c9b18d197f398b0e4206cea9acf9c197"}, {"type_name": "service_msgs/msg/ServiceEventInfo", "hash_string": "RIHS01_41bcbbe07a75c9b52bc96bfd5c24d7f0fc0a08c0cb7921b3373c5732345a6f45"}, {"type_name": "sl_vcu_all/srv/AddCanFilter_Event", "hash_string": "RIHS01_177fdc551ac3369cf7e980b6606ab53e34e5920e10969370f9a20148714d4936"}, {"type_name": "sl_vcu_all/srv/AddCanFilter_Request", "hash_string": "RIHS01_2acb9afdf693db9b6722749eb21078afff7247e8c9314f61f05dbc2ddb4c8559"}, {"type_name": "sl_vcu_all/srv/AddCanFilter_Response", "hash_string": "RIHS01_6a3e005185640a690ae794365c6caceb7f2084961fda854da923e46b9baa0352"}]}