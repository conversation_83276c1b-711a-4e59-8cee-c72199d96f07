// generated from rosidl_adapter/resource/srv.idl.em
// with input from sl_vcu_all/srv/CheckNodeStatus.srv
// generated code does not contain a copyright notice


module sl_vcu_all {
  module srv {
    @verbatim (language="comment", text=
      "Request - empty")
    struct CheckNodeStatus_Request {
      uint8 structure_needs_at_least_one_member;
    };
    @verbatim (language="comment", text=
      "Response")
    struct CheckNodeStatus_Response {
      @verbatim (language="comment", text=
        "Whether the node is ready to process messages")
      boolean is_ready;

      @verbatim (language="comment", text=
        "Status message or error description")
      string status;
    };
  };
};
