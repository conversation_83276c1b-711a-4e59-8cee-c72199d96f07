{"type_description_msg": {"type_description": {"type_name": "sl_vcu_all/srv/CheckNodeStatus", "fields": [{"name": "request_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/CheckNodeStatus_Request"}, "default_value": ""}, {"name": "response_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/CheckNodeStatus_Response"}, "default_value": ""}, {"name": "event_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/CheckNodeStatus_Event"}, "default_value": ""}]}, "referenced_type_descriptions": [{"type_name": "builtin_interfaces/msg/Time", "fields": [{"name": "sec", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "nanosec", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "service_msgs/msg/ServiceEventInfo", "fields": [{"name": "event_type", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}, {"name": "client_gid", "type": {"type_id": 51, "capacity": 16, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sequence_number", "type": {"type_id": 8, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/CheckNodeStatus_Event", "fields": [{"name": "info", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "service_msgs/msg/ServiceEventInfo"}, "default_value": ""}, {"name": "request", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/CheckNodeStatus_Request"}, "default_value": ""}, {"name": "response", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/CheckNodeStatus_Response"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/CheckNodeStatus_Request", "fields": [{"name": "structure_needs_at_least_one_member", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/CheckNodeStatus_Response", "fields": [{"name": "is_ready", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "status", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}]}, "type_hashes": [{"type_name": "sl_vcu_all/srv/CheckNodeStatus", "hash_string": "RIHS01_c48c3226efd7b91d40d65d664bec07b0af717c0dc813caf7a7356146a657c921"}, {"type_name": "builtin_interfaces/msg/Time", "hash_string": "RIHS01_b106235e25a4c5ed35098aa0a61a3ee9c9b18d197f398b0e4206cea9acf9c197"}, {"type_name": "service_msgs/msg/ServiceEventInfo", "hash_string": "RIHS01_41bcbbe07a75c9b52bc96bfd5c24d7f0fc0a08c0cb7921b3373c5732345a6f45"}, {"type_name": "sl_vcu_all/srv/CheckNodeStatus_Event", "hash_string": "RIHS01_3240dfe2166b677966cfb9b7c11cfb3b236b8e994bb82c3496cfb1214932b94e"}, {"type_name": "sl_vcu_all/srv/CheckNodeStatus_Request", "hash_string": "RIHS01_1ce76dbbba9da35792235a5d57f41d115f20376fcb3e47812cf9c3169f68ea26"}, {"type_name": "sl_vcu_all/srv/CheckNodeStatus_Response", "hash_string": "RIHS01_632505618f26b39a922ed55620197c1bd12a242477041c735cdd04e3ace43dbf"}]}