{"type_description_msg": {"type_description": {"type_name": "sl_vcu_all/srv/LedControl", "fields": [{"name": "request_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/LedControl_Request"}, "default_value": ""}, {"name": "response_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/LedControl_Response"}, "default_value": ""}, {"name": "event_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/LedControl_Event"}, "default_value": ""}]}, "referenced_type_descriptions": [{"type_name": "builtin_interfaces/msg/Time", "fields": [{"name": "sec", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "nanosec", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "service_msgs/msg/ServiceEventInfo", "fields": [{"name": "event_type", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}, {"name": "client_gid", "type": {"type_id": 51, "capacity": 16, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sequence_number", "type": {"type_id": 8, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/msg/ChannelData", "fields": [{"name": "parts", "type": {"type_id": 147, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sync_parts", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "parts_data", "type": {"type_id": 145, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/msg/PartData"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/msg/PartData", "fields": [{"name": "mode", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "green_brightness", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "red_brightness", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "blue_brightness", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "frequency", "type": {"type_id": 10, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "speed", "type": {"type_id": 10, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "marquee_direction", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "on_time_duty", "type": {"type_id": 10, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/LedControl_Event", "fields": [{"name": "info", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "service_msgs/msg/ServiceEventInfo"}, "default_value": ""}, {"name": "request", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/LedControl_Request"}, "default_value": ""}, {"name": "response", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "sl_vcu_all/srv/LedControl_Response"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/LedControl_Request", "fields": [{"name": "channels", "type": {"type_id": 147, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sync_channels", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "channel_data", "type": {"type_id": 145, "capacity": 0, "string_capacity": 0, "nested_type_name": "sl_vcu_all/msg/ChannelData"}, "default_value": ""}]}, {"type_name": "sl_vcu_all/srv/LedControl_Response", "fields": [{"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "affected_channels", "type": {"type_id": 147, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}]}, "type_hashes": [{"type_name": "sl_vcu_all/srv/LedControl", "hash_string": "RIHS01_208ed46c49d5b2fa97e825d8c8a846cbd64e986e3b9dd51d8ea08a5ea4c9be6d"}, {"type_name": "builtin_interfaces/msg/Time", "hash_string": "RIHS01_b106235e25a4c5ed35098aa0a61a3ee9c9b18d197f398b0e4206cea9acf9c197"}, {"type_name": "service_msgs/msg/ServiceEventInfo", "hash_string": "RIHS01_41bcbbe07a75c9b52bc96bfd5c24d7f0fc0a08c0cb7921b3373c5732345a6f45"}, {"type_name": "sl_vcu_all/msg/ChannelData", "hash_string": "RIHS01_2dd73ef932ed81bcdfb0bd1a4b34917e6392802582335c1cb5eb17ab89fa306c"}, {"type_name": "sl_vcu_all/msg/PartData", "hash_string": "RIHS01_44e09f9f38472e212fec22e7182c212d97ba1b6501c50e89fb31199e908f32c3"}, {"type_name": "sl_vcu_all/srv/LedControl_Event", "hash_string": "RIHS01_598875d1be096a0aeb1d671d4af9ce575c6b0ed2cde16e1af6a5c2ce9fd24f5e"}, {"type_name": "sl_vcu_all/srv/LedControl_Request", "hash_string": "RIHS01_338048851eb60a16cfe46b2f7759f4636059eff702b6d7f00fda576adaa3dd8e"}, {"type_name": "sl_vcu_all/srv/LedControl_Response", "hash_string": "RIHS01_24cf661dea2f8a991addcb53aa3c153acc628cfb34636f600550b6f14d0e6cfc"}]}