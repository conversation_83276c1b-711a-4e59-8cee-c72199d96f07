[0.000000] (-) TimerEvent: {}
[0.000648] (sl_vcu_all) JobQueued: {'identifier': 'sl_vcu_all', 'dependencies': OrderedDict()}
[0.000703] (sl_vcu_all) JobStarted: {'identifier': 'sl_vcu_all'}
[0.011700] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'cmake'}
[0.012356] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 60526 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', 'SSH_TTY': '/dev/pts/1', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '4101', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 60526 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.099683] (-) TimerEvent: {}
[0.173191] (sl_vcu_all) StdoutLine: {'line': b'-- The C compiler identification is GNU 13.3.0\n'}
[0.199799] (-) TimerEvent: {}
[0.300092] (-) TimerEvent: {}
[0.304246] (sl_vcu_all) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 13.3.0\n'}
[0.332493] (sl_vcu_all) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.400218] (-) TimerEvent: {}
[0.441326] (sl_vcu_all) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.452515] (sl_vcu_all) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.453129] (sl_vcu_all) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.453887] (sl_vcu_all) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.466138] (sl_vcu_all) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.500346] (-) TimerEvent: {}
[0.580180] (sl_vcu_all) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.591202] (sl_vcu_all) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.591631] (sl_vcu_all) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.592227] (sl_vcu_all) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.600448] (-) TimerEvent: {}
[0.608000] (sl_vcu_all) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[0.700620] (-) TimerEvent: {}
[0.789414] (sl_vcu_all) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter \n'}
[0.800728] (-) TimerEvent: {}
[0.901013] (-) TimerEvent: {}
[0.963207] (sl_vcu_all) StdoutLine: {'line': b'-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)\n'}
[1.001131] (-) TimerEvent: {}
[1.050861] (sl_vcu_all) StdoutLine: {'line': b'-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)\n'}
[1.079204] (sl_vcu_all) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)\n'}
[1.101267] (-) TimerEvent: {}
[1.115482] (sl_vcu_all) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.153360] (sl_vcu_all) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.201411] (-) TimerEvent: {}
[1.301722] (sl_vcu_all) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)\n'}
[1.301921] (-) TimerEvent: {}
[1.309337] (sl_vcu_all) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)\n'}
[1.402101] (-) TimerEvent: {}
[1.438798] (sl_vcu_all) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  \n'}
[1.466309] (sl_vcu_all) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/jazzy/include (Required is at least version "2.13") \n'}
[1.494387] (sl_vcu_all) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.502208] (-) TimerEvent: {}
[1.602455] (-) TimerEvent: {}
[1.630893] (sl_vcu_all) StdoutLine: {'line': b'-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)\n'}
[1.657083] (sl_vcu_all) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.6.0 (/opt/ros/jazzy/share/rosidl_default_generators/cmake)\n'}
[1.689327] (sl_vcu_all) StdoutLine: {'line': b'-- Found rosidl_adapter: 4.6.5 (/opt/ros/jazzy/share/rosidl_adapter/cmake)\n'}
[1.698199] (sl_vcu_all) StdoutLine: {'line': b'-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)\n'}
[1.702613] (-) TimerEvent: {}
[1.727593] (sl_vcu_all) StdoutLine: {'line': b'-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)\n'}
[1.770459] (sl_vcu_all) StdoutLine: {'line': b'-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)\n'}
[1.780929] (sl_vcu_all) StdoutLine: {'line': b'-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)\n'}
[1.802732] (-) TimerEvent: {}
[1.903009] (-) TimerEvent: {}
[2.002480] (sl_vcu_all) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)\n'}
[2.003110] (-) TimerEvent: {}
[2.036715] (sl_vcu_all) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)\n'}
[2.038579] (sl_vcu_all) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[2.038883] (sl_vcu_all) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[2.046563] (sl_vcu_all) StdoutLine: {'line': b'-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)\n'}
[2.084026] (sl_vcu_all) StdoutLine: {'line': b'-- Found PkgConfig: /usr/bin/pkg-config (found version "1.8.1") \n'}
[2.085079] (sl_vcu_all) StdoutLine: {'line': b"-- Checking for module 'libmodbus'\n"}
[2.103242] (-) TimerEvent: {}
[2.108246] (sl_vcu_all) StdoutLine: {'line': b'--   Found libmodbus, version 3.1.10\n'}
[2.151725] (sl_vcu_all) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[2.203366] (-) TimerEvent: {}
[2.303600] (-) TimerEvent: {}
[2.403846] (-) TimerEvent: {}
[2.504224] (-) TimerEvent: {}
[2.604479] (-) TimerEvent: {}
[2.704740] (-) TimerEvent: {}
[2.804990] (-) TimerEvent: {}
[2.905204] (-) TimerEvent: {}
[2.947707] (sl_vcu_all) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.12.0 (/opt/ros/jazzy/share/ament_cmake_ros/cmake)\n'}
[3.005325] (-) TimerEvent: {}
[3.105612] (-) TimerEvent: {}
[3.205875] (-) TimerEvent: {}
[3.306123] (-) TimerEvent: {}
[3.406369] (-) TimerEvent: {}
[3.506599] (-) TimerEvent: {}
[3.606873] (-) TimerEvent: {}
[3.707121] (-) TimerEvent: {}
[3.807373] (-) TimerEvent: {}
[3.843768] (sl_vcu_all) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[3.907491] (-) TimerEvent: {}
[4.007759] (-) TimerEvent: {}
[4.108006] (-) TimerEvent: {}
[4.189771] (sl_vcu_all) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[4.197280] (sl_vcu_all) StdoutLine: {'line': b'-- Found python_cmake_module: 0.11.1 (/opt/ros/jazzy/share/python_cmake_module/cmake)\n'}
[4.208127] (-) TimerEvent: {}
[4.308383] (-) TimerEvent: {}
[4.347150] (sl_vcu_all) StdoutLine: {'line': b'-- Found PythonExtra: /usr/bin/python3  \n'}
[4.408500] (-) TimerEvent: {}
[4.508791] (-) TimerEvent: {}
[4.609037] (-) TimerEvent: {}
[4.709314] (-) TimerEvent: {}
[4.809628] (-) TimerEvent: {}
[4.824355] (sl_vcu_all) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed \n'}
[4.909845] (-) TimerEvent: {}
[4.987219] (sl_vcu_all) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[4.988532] (sl_vcu_all) StdoutLine: {'line': b'-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include>\n'}
[4.988675] (sl_vcu_all) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[4.996723] (sl_vcu_all) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[4.996858] (sl_vcu_all) StdoutLine: {'line': b"-- Configured 'flake8' exclude dirs and/or files: \n"}
[5.009969] (-) TimerEvent: {}
[5.012111] (sl_vcu_all) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[5.020301] (sl_vcu_all) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[5.076350] (sl_vcu_all) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[5.076559] (sl_vcu_all) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[5.085224] (sl_vcu_all) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[5.096913] (sl_vcu_all) StdoutLine: {'line': b'-- Configuring done (5.1s)\n'}
[5.110084] (-) TimerEvent: {}
[5.210345] (-) TimerEvent: {}
[5.219357] (sl_vcu_all) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[5.251104] (sl_vcu_all) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all\n'}
[5.267669] (sl_vcu_all) CommandEnded: {'returncode': 0}
[5.268321] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'build'}
[5.269293] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 60526 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', 'SSH_TTY': '/dev/pts/1', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '4101', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 60526 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[5.310479] (-) TimerEvent: {}
[5.339580] (sl_vcu_all) StdoutLine: {'line': b'[  0%] \x1b[34m\x1b[1mGenerating type hashes for ROS interfaces\x1b[0m\n'}
[5.350779] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_sl_vcu_all\n'}
[5.410613] (-) TimerEvent: {}
[5.510859] (-) TimerEvent: {}
[5.611104] (-) TimerEvent: {}
[5.628852] (sl_vcu_all) StdoutLine: {'line': b'running egg_info\n'}
[5.629938] (sl_vcu_all) StdoutLine: {'line': b'creating sl_vcu_all.egg-info\n'}
[5.658533] (sl_vcu_all) StdoutLine: {'line': b'writing sl_vcu_all.egg-info/PKG-INFO\n'}
[5.658839] (sl_vcu_all) StdoutLine: {'line': b'writing dependency_links to sl_vcu_all.egg-info/dependency_links.txt\n'}
[5.659081] (sl_vcu_all) StdoutLine: {'line': b'writing top-level names to sl_vcu_all.egg-info/top_level.txt\n'}
[5.659342] (sl_vcu_all) StdoutLine: {'line': b"writing manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[5.711220] (-) TimerEvent: {}
[5.743964] (sl_vcu_all) StdoutLine: {'line': b"reading manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[5.744580] (sl_vcu_all) StdoutLine: {'line': b"writing manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[5.775461] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_build_sl_vcu_all_egg\n'}
[5.811336] (-) TimerEvent: {}
[5.911581] (-) TimerEvent: {}
[6.011813] (-) TimerEvent: {}
[6.112055] (-) TimerEvent: {}
[6.212301] (-) TimerEvent: {}
[6.287667] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target sl_vcu_all__rosidl_generator_type_description\n'}
[6.302052] (sl_vcu_all) StdoutLine: {'line': b'[  1%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[6.312418] (-) TimerEvent: {}
[6.315600] (sl_vcu_all) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[6.412538] (-) TimerEvent: {}
[6.512779] (-) TimerEvent: {}
[6.613028] (-) TimerEvent: {}
[6.713269] (-) TimerEvent: {}
[6.813518] (-) TimerEvent: {}
[6.913773] (-) TimerEvent: {}
[7.014014] (-) TimerEvent: {}
[7.114261] (-) TimerEvent: {}
[7.214497] (-) TimerEvent: {}
[7.314722] (-) TimerEvent: {}
[7.414962] (-) TimerEvent: {}
[7.515203] (-) TimerEvent: {}
[7.615448] (-) TimerEvent: {}
[7.715672] (-) TimerEvent: {}
[7.815913] (-) TimerEvent: {}
[7.916159] (-) TimerEvent: {}
[8.016376] (-) TimerEvent: {}
[8.119741] (-) TimerEvent: {}
[8.179908] (sl_vcu_all) StdoutLine: {'line': b'[  2%] Built target sl_vcu_all__cpp\n'}
[8.219843] (-) TimerEvent: {}
[8.320081] (-) TimerEvent: {}
[8.420526] (-) TimerEvent: {}
[8.449746] (sl_vcu_all) StdoutLine: {'line': b'[  4%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/add_can_filter__description.c.o\x1b[0m\n'}
[8.450134] (sl_vcu_all) StdoutLine: {'line': b'[  4%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/add_can_filter__type_support.c.o\x1b[0m\n'}
[8.450295] (sl_vcu_all) StdoutLine: {'line': b'[  4%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/add_can_filter__functions.c.o\x1b[0m\n'}
[8.450413] (sl_vcu_all) StdoutLine: {'line': b'[  4%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/check_node_status__description.c.o\x1b[0m\n'}
[8.509190] (sl_vcu_all) StdoutLine: {'line': b'[  5%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/check_node_status__functions.c.o\x1b[0m\n'}
[8.515649] (sl_vcu_all) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/check_node_status__type_support.c.o\x1b[0m\n'}
[8.515836] (sl_vcu_all) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/led_control__description.c.o\x1b[0m\n'}
[8.521560] (-) TimerEvent: {}
[8.553577] (sl_vcu_all) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/led_control__functions.c.o\x1b[0m\n'}
[8.571916] (sl_vcu_all) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/led_control__type_support.c.o\x1b[0m\n'}
[8.583119] (sl_vcu_all) StdoutLine: {'line': b'[  8%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_info__description.c.o\x1b[0m\n'}
[8.602574] (sl_vcu_all) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_info__functions.c.o\x1b[0m\n'}
[8.621660] (-) TimerEvent: {}
[8.622732] (sl_vcu_all) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_info__type_support.c.o\x1b[0m\n'}
[8.639234] (sl_vcu_all) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_state__description.c.o\x1b[0m\n'}
[8.649214] (sl_vcu_all) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_state__functions.c.o\x1b[0m\n'}
[8.663342] (sl_vcu_all) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_state__type_support.c.o\x1b[0m\n'}
[8.668575] (sl_vcu_all) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/bumper_state__description.c.o\x1b[0m\n'}
[8.697747] (sl_vcu_all) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/bumper_state__functions.c.o\x1b[0m\n'}
[8.705768] (sl_vcu_all) StdoutLine: {'line': b'[ 13%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/bumper_state__type_support.c.o\x1b[0m\n'}
[8.715675] (sl_vcu_all) StdoutLine: {'line': b'[ 13%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/can_frame__description.c.o\x1b[0m\n'}
[8.721756] (-) TimerEvent: {}
[8.723217] (sl_vcu_all) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/can_frame__functions.c.o\x1b[0m\n'}
[8.746158] (sl_vcu_all) StdoutLine: {'line': b'[ 15%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/can_frame__type_support.c.o\x1b[0m\n'}
[8.760792] (sl_vcu_all) StdoutLine: {'line': b'[ 15%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/battery_status__description.c.o\x1b[0m\n'}
[8.768805] (sl_vcu_all) StdoutLine: {'line': b'[ 16%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/battery_status__functions.c.o\x1b[0m\n'}
[8.786413] (sl_vcu_all) StdoutLine: {'line': b'[ 16%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/part_data__description.c.o\x1b[0m\n'}
[8.786639] (sl_vcu_all) StdoutLine: {'line': b'[ 17%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/battery_status__type_support.c.o\x1b[0m\n'}
[8.813849] (sl_vcu_all) StdoutLine: {'line': b'[ 18%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/part_data__functions.c.o\x1b[0m\n'}
[8.821860] (-) TimerEvent: {}
[8.826875] (sl_vcu_all) StdoutLine: {'line': b'[ 18%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/part_data__type_support.c.o\x1b[0m\n'}
[8.833329] (sl_vcu_all) StdoutLine: {'line': b'[ 19%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/channel_data__description.c.o\x1b[0m\n'}
[8.835012] (sl_vcu_all) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/channel_data__functions.c.o\x1b[0m\n'}
[8.866934] (sl_vcu_all) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/channel_data__type_support.c.o\x1b[0m\n'}
[8.875877] (sl_vcu_all) StdoutLine: {'line': b'[ 21%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/jack_status__description.c.o\x1b[0m\n'}
[8.884639] (sl_vcu_all) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/jack_status__functions.c.o\x1b[0m\n'}
[8.898291] (sl_vcu_all) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/jack_status__type_support.c.o\x1b[0m\n'}
[8.907642] (sl_vcu_all) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/action/detail/jack_control__description.c.o\x1b[0m\n'}
[8.922559] (-) TimerEvent: {}
[8.928438] (sl_vcu_all) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/action/detail/jack_control__functions.c.o\x1b[0m\n'}
[8.939019] (sl_vcu_all) StdoutLine: {'line': b'[ 24%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/action/detail/jack_control__type_support.c.o\x1b[0m\n'}
[9.022656] (-) TimerEvent: {}
[9.112491] (sl_vcu_all) StdoutLine: {'line': b'[ 25%] \x1b[32m\x1b[1mLinking C shared library libsl_vcu_all__rosidl_generator_c.so\x1b[0m\n'}
[9.122748] (-) TimerEvent: {}
[9.165463] (sl_vcu_all) StdoutLine: {'line': b'[ 25%] Built target sl_vcu_all__rosidl_generator_c\n'}
[9.182619] (sl_vcu_all) StdoutLine: {'line': b'[ 26%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[9.182800] (sl_vcu_all) StdoutLine: {'line': b'[ 26%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[9.182891] (sl_vcu_all) StdoutLine: {'line': b'[ 26%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[9.185702] (sl_vcu_all) StdoutLine: {'line': b'[ 27%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[9.222837] (-) TimerEvent: {}
[9.323067] (-) TimerEvent: {}
[9.423296] (-) TimerEvent: {}
[9.523537] (-) TimerEvent: {}
[9.624645] (-) TimerEvent: {}
[9.724856] (-) TimerEvent: {}
[9.825087] (-) TimerEvent: {}
[9.925297] (-) TimerEvent: {}
[10.025496] (-) TimerEvent: {}
[10.126543] (-) TimerEvent: {}
[10.226754] (-) TimerEvent: {}
[10.326957] (-) TimerEvent: {}
[10.427168] (-) TimerEvent: {}
[10.527378] (-) TimerEvent: {}
[10.542194] (sl_vcu_all) StdoutLine: {'line': b'[ 27%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/srv/add_can_filter__type_support.cpp.o\x1b[0m\n'}
[10.627466] (-) TimerEvent: {}
[10.727883] (-) TimerEvent: {}
[10.830629] (-) TimerEvent: {}
[10.930853] (-) TimerEvent: {}
[10.944658] (sl_vcu_all) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/srv/detail/add_can_filter__type_support.c.o\x1b[0m\n'}
[11.009310] (sl_vcu_all) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/srv/detail/check_node_status__type_support.c.o\x1b[0m\n'}
[11.030939] (-) TimerEvent: {}
[11.034756] (sl_vcu_all) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/srv/check_node_status__type_support.cpp.o\x1b[0m\n'}
[11.071327] (sl_vcu_all) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/srv/detail/led_control__type_support.c.o\x1b[0m\n'}
[11.131028] (-) TimerEvent: {}
[11.144137] (sl_vcu_all) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/motor_info__type_support.c.o\x1b[0m\n'}
[11.197579] (sl_vcu_all) StdoutLine: {'line': b'[ 32%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/motor_state__type_support.c.o\x1b[0m\n'}
[11.231125] (-) TimerEvent: {}
[11.245551] (sl_vcu_all) StdoutLine: {'line': b'[ 32%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/bumper_state__type_support.c.o\x1b[0m\n'}
[11.295091] (sl_vcu_all) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/can_frame__type_support.c.o\x1b[0m\n'}
[11.331224] (-) TimerEvent: {}
[11.331571] (sl_vcu_all) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/srv/detail/dds_fastrtps/add_can_filter__type_support.cpp.o\x1b[0m\n'}
[11.347337] (sl_vcu_all) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/battery_status__type_support.c.o\x1b[0m\n'}
[11.401582] (sl_vcu_all) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/part_data__type_support.c.o\x1b[0m\n'}
[11.431321] (-) TimerEvent: {}
[11.448097] (sl_vcu_all) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/channel_data__type_support.c.o\x1b[0m\n'}
[11.483873] (sl_vcu_all) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/srv/detail/add_can_filter__type_support_c.cpp.o\x1b[0m\n'}
[11.505893] (sl_vcu_all) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/jack_status__type_support.c.o\x1b[0m\n'}
[11.516755] (sl_vcu_all) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/srv/led_control__type_support.cpp.o\x1b[0m\n'}
[11.531418] (-) TimerEvent: {}
[11.555048] (sl_vcu_all) StdoutLine: {'line': b'[ 37%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/action/detail/jack_control__type_support.c.o\x1b[0m\n'}
[11.631536] (-) TimerEvent: {}
[11.641218] (sl_vcu_all) StdoutLine: {'line': b'[ 37%] \x1b[32m\x1b[1mLinking C shared library libsl_vcu_all__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[11.689477] (sl_vcu_all) StdoutLine: {'line': b'[ 37%] Built target sl_vcu_all__rosidl_typesupport_introspection_c\n'}
[11.703561] (sl_vcu_all) StdoutLine: {'line': b'[ 37%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/srv/detail/check_node_status__type_support_c.cpp.o\x1b[0m\n'}
[11.731609] (-) TimerEvent: {}
[11.831868] (-) TimerEvent: {}
[11.932122] (-) TimerEvent: {}
[12.032566] (-) TimerEvent: {}
[12.097381] (sl_vcu_all) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/motor_info__type_support.cpp.o\x1b[0m\n'}
[12.099380] (sl_vcu_all) StdoutLine: {'line': b'[ 39%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/srv/detail/dds_fastrtps/check_node_status__type_support.cpp.o\x1b[0m\n'}
[12.104769] (sl_vcu_all) StdoutLine: {'line': b'[ 40%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[12.134552] (-) TimerEvent: {}
[12.234798] (-) TimerEvent: {}
[12.319587] (sl_vcu_all) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/srv/detail/led_control__type_support_c.cpp.o\x1b[0m\n'}
[12.334889] (-) TimerEvent: {}
[12.435119] (-) TimerEvent: {}
[12.470387] (sl_vcu_all) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/motor_state__type_support.cpp.o\x1b[0m\n'}
[12.535208] (-) TimerEvent: {}
[12.635452] (-) TimerEvent: {}
[12.735708] (-) TimerEvent: {}
[12.836120] (-) TimerEvent: {}
[12.842242] (sl_vcu_all) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/srv/detail/dds_fastrtps/led_control__type_support.cpp.o\x1b[0m\n'}
[12.848582] (sl_vcu_all) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/bumper_state__type_support.cpp.o\x1b[0m\n'}
[12.936209] (-) TimerEvent: {}
[12.940137] (sl_vcu_all) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/motor_info__type_support_c.cpp.o\x1b[0m\n'}
[13.036303] (-) TimerEvent: {}
[13.136556] (-) TimerEvent: {}
[13.219043] (sl_vcu_all) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/can_frame__type_support.cpp.o\x1b[0m\n'}
[13.236647] (-) TimerEvent: {}
[13.336889] (-) TimerEvent: {}
[13.437144] (-) TimerEvent: {}
[13.504228] (sl_vcu_all) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/motor_state__type_support_c.cpp.o\x1b[0m\n'}
[13.518699] (sl_vcu_all) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/srv/add_can_filter__type_support.cpp.o\x1b[0m\n'}
[13.537559] (-) TimerEvent: {}
[13.581570] (sl_vcu_all) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/srv/check_node_status__type_support.cpp.o\x1b[0m\n'}
[13.598678] (sl_vcu_all) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/battery_status__type_support.cpp.o\x1b[0m\n'}
[13.637655] (-) TimerEvent: {}
[13.644613] (sl_vcu_all) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/srv/led_control__type_support.cpp.o\x1b[0m\n'}
[13.706204] (sl_vcu_all) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/motor_info__type_support.cpp.o\x1b[0m\n'}
[13.737752] (-) TimerEvent: {}
[13.748466] (sl_vcu_all) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/motor_info__type_support.cpp.o\x1b[0m\n'}
[13.764014] (sl_vcu_all) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/motor_state__type_support.cpp.o\x1b[0m\n'}
[13.820005] (sl_vcu_all) StdoutLine: {'line': b'[ 49%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/bumper_state__type_support.cpp.o\x1b[0m\n'}
[13.837850] (-) TimerEvent: {}
[13.879581] (sl_vcu_all) StdoutLine: {'line': b'[ 49%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/can_frame__type_support.cpp.o\x1b[0m\n'}
[13.932974] (sl_vcu_all) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/battery_status__type_support.cpp.o\x1b[0m\n'}
[13.937948] (-) TimerEvent: {}
[13.973853] (sl_vcu_all) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/part_data__type_support.cpp.o\x1b[0m\n'}
[13.990290] (sl_vcu_all) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/part_data__type_support.cpp.o\x1b[0m\n'}
[14.038045] (-) TimerEvent: {}
[14.046209] (sl_vcu_all) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/channel_data__type_support.cpp.o\x1b[0m\n'}
[14.085728] (sl_vcu_all) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/bumper_state__type_support_c.cpp.o\x1b[0m\n'}
[14.102254] (sl_vcu_all) StdoutLine: {'line': b'[ 54%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/jack_status__type_support.cpp.o\x1b[0m\n'}
[14.138147] (-) TimerEvent: {}
[14.159630] (sl_vcu_all) StdoutLine: {'line': b'[ 54%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/action/jack_control__type_support.cpp.o\x1b[0m\n'}
[14.236827] (sl_vcu_all) StdoutLine: {'line': b'[ 55%] \x1b[32m\x1b[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_c.so\x1b[0m\n'}
[14.238232] (-) TimerEvent: {}
[14.296989] (sl_vcu_all) StdoutLine: {'line': b'[ 55%] Built target sl_vcu_all__rosidl_typesupport_c\n'}
[14.309260] (sl_vcu_all) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/motor_state__type_support.cpp.o\x1b[0m\n'}
[14.328487] (sl_vcu_all) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/channel_data__type_support.cpp.o\x1b[0m\n'}
[14.338329] (-) TimerEvent: {}
[14.349350] (sl_vcu_all) StdoutLine: {'line': b'[ 58%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[14.438419] (-) TimerEvent: {}
[14.538663] (-) TimerEvent: {}
[14.638913] (-) TimerEvent: {}
[14.643553] (sl_vcu_all) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/can_frame__type_support_c.cpp.o\x1b[0m\n'}
[14.702573] (sl_vcu_all) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/jack_status__type_support.cpp.o\x1b[0m\n'}
[14.739001] (-) TimerEvent: {}
[14.839418] (-) TimerEvent: {}
[14.895168] (sl_vcu_all) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/bumper_state__type_support.cpp.o\x1b[0m\n'}
[14.939520] (-) TimerEvent: {}
[15.039770] (-) TimerEvent: {}
[15.081438] (sl_vcu_all) StdoutLine: {'line': b'[ 59%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/action/jack_control__type_support.cpp.o\x1b[0m\n'}
[15.139866] (-) TimerEvent: {}
[15.202270] (sl_vcu_all) StdoutLine: {'line': b'[ 60%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/battery_status__type_support_c.cpp.o\x1b[0m\n'}
[15.239962] (-) TimerEvent: {}
[15.340208] (-) TimerEvent: {}
[15.440442] (-) TimerEvent: {}
[15.464590] (sl_vcu_all) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/can_frame__type_support.cpp.o\x1b[0m\n'}
[15.540546] (-) TimerEvent: {}
[15.640803] (-) TimerEvent: {}
[15.741236] (-) TimerEvent: {}
[15.758529] (sl_vcu_all) StdoutLine: {'line': b'[ 62%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/part_data__type_support_c.cpp.o\x1b[0m\n'}
[15.841344] (-) TimerEvent: {}
[15.908275] (sl_vcu_all) StdoutLine: {'line': b'[ 63%] \x1b[32m\x1b[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_cpp.so\x1b[0m\n'}
[15.941444] (-) TimerEvent: {}
[16.023120] (sl_vcu_all) StdoutLine: {'line': b'[ 63%] Built target sl_vcu_all__rosidl_typesupport_cpp\n'}
[16.035235] (sl_vcu_all) StdoutLine: {'line': b'[ 64%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/battery_status__type_support.cpp.o\x1b[0m\n'}
[16.041548] (-) TimerEvent: {}
[16.059692] (sl_vcu_all) StdoutLine: {'line': b'[ 64%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/channel_data__type_support_c.cpp.o\x1b[0m\n'}
[16.087164] (sl_vcu_all) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/srv/detail/add_can_filter__type_support.cpp.o\x1b[0m\n'}
[16.141648] (-) TimerEvent: {}
[16.244687] (-) TimerEvent: {}
[16.344933] (-) TimerEvent: {}
[16.346707] (sl_vcu_all) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/part_data__type_support.cpp.o\x1b[0m\n'}
[16.445028] (-) TimerEvent: {}
[16.545490] (-) TimerEvent: {}
[16.609527] (sl_vcu_all) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/srv/detail/check_node_status__type_support.cpp.o\x1b[0m\n'}
[16.624646] (sl_vcu_all) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/jack_status__type_support_c.cpp.o\x1b[0m\n'}
[16.631894] (sl_vcu_all) StdoutLine: {'line': b'[ 67%] \x1b[32mBuilding CXX object CMakeFiles/can_frame_dispatcher_node.dir/src/can_frame_dispatcher_node.cpp.o\x1b[0m\n'}
[16.645557] (-) TimerEvent: {}
[16.745806] (-) TimerEvent: {}
[16.846345] (-) TimerEvent: {}
[16.927646] (sl_vcu_all) StdoutLine: {'line': b'[ 68%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/channel_data__type_support.cpp.o\x1b[0m\n'}
[16.946567] (-) TimerEvent: {}
[17.046950] (-) TimerEvent: {}
[17.125981] (sl_vcu_all) StdoutLine: {'line': b'[ 69%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/srv/detail/led_control__type_support.cpp.o\x1b[0m\n'}
[17.147049] (-) TimerEvent: {}
[17.197917] (sl_vcu_all) StdoutLine: {'line': b'[ 70%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/action/detail/jack_control__type_support_c.cpp.o\x1b[0m\n'}
[17.247149] (-) TimerEvent: {}
[17.347392] (-) TimerEvent: {}
[17.447648] (-) TimerEvent: {}
[17.548560] (-) TimerEvent: {}
[17.582888] (sl_vcu_all) StdoutLine: {'line': b'[ 71%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/jack_status__type_support.cpp.o\x1b[0m\n'}
[17.648657] (-) TimerEvent: {}
[17.748916] (-) TimerEvent: {}
[17.851699] (-) TimerEvent: {}
[17.888604] (sl_vcu_all) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/motor_info__type_support.cpp.o\x1b[0m\n'}
[17.948194] (sl_vcu_all) StdoutLine: {'line': b'[ 72%] \x1b[32m\x1b[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[17.951798] (-) TimerEvent: {}
[18.052051] (-) TimerEvent: {}
[18.059704] (sl_vcu_all) StdoutLine: {'line': b'[ 72%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_c\n'}
[18.071492] (sl_vcu_all) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/motor_state__type_support.cpp.o\x1b[0m\n'}
[18.152150] (-) TimerEvent: {}
[18.174816] (sl_vcu_all) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/action/detail/dds_fastrtps/jack_control__type_support.cpp.o\x1b[0m\n'}
[18.252334] (-) TimerEvent: {}
[18.296567] (sl_vcu_all) StdoutLine: {'line': b'[ 73%] \x1b[32mBuilding CXX object CMakeFiles/zl_motor_controller_node.dir/src/zl_motor_controller_node.cpp.o\x1b[0m\n'}
[18.352432] (-) TimerEvent: {}
[18.452683] (-) TimerEvent: {}
[18.453047] (sl_vcu_all) StdoutLine: {'line': b'[ 74%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/bumper_state__type_support.cpp.o\x1b[0m\n'}
[18.553560] (-) TimerEvent: {}
[18.653996] (-) TimerEvent: {}
[18.754234] (-) TimerEvent: {}
[18.851815] (sl_vcu_all) StdoutLine: {'line': b'[ 74%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/can_frame__type_support.cpp.o\x1b[0m\n'}
[18.854331] (-) TimerEvent: {}
[18.954564] (-) TimerEvent: {}
[19.055560] (-) TimerEvent: {}
[19.155813] (-) TimerEvent: {}
[19.248917] (sl_vcu_all) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/battery_status__type_support.cpp.o\x1b[0m\n'}
[19.255910] (-) TimerEvent: {}
[19.356335] (-) TimerEvent: {}
[19.416997] (sl_vcu_all) StdoutLine: {'line': b'[ 76%] \x1b[32m\x1b[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[19.456557] (-) TimerEvent: {}
[19.544557] (sl_vcu_all) StdoutLine: {'line': b'[ 76%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_cpp\n'}
[19.555814] (sl_vcu_all) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/part_data__type_support.cpp.o\x1b[0m\n'}
[19.556640] (-) TimerEvent: {}
[19.654359] (sl_vcu_all) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding CXX object CMakeFiles/zl_motor_modbus_controller_node.dir/src/zl_motor_modbus_controller_node.cpp.o\x1b[0m\n'}
[19.656739] (-) TimerEvent: {}
[19.759698] (-) TimerEvent: {}
[19.860131] (-) TimerEvent: {}
[19.935940] (sl_vcu_all) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/channel_data__type_support.cpp.o\x1b[0m\n'}
[19.960229] (-) TimerEvent: {}
[20.060646] (-) TimerEvent: {}
[20.160886] (-) TimerEvent: {}
[20.261133] (-) TimerEvent: {}
[20.361380] (-) TimerEvent: {}
[20.397468] (sl_vcu_all) StdoutLine: {'line': b'[ 79%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/jack_status__type_support.cpp.o\x1b[0m\n'}
[20.461486] (-) TimerEvent: {}
[20.562567] (-) TimerEvent: {}
[20.662815] (-) TimerEvent: {}
[20.763055] (-) TimerEvent: {}
[20.785574] (sl_vcu_all) StdoutLine: {'line': b'[ 79%] \x1b[32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/action/detail/jack_control__type_support.cpp.o\x1b[0m\n'}
[20.863154] (-) TimerEvent: {}
[20.963559] (-) TimerEvent: {}
[21.064560] (-) TimerEvent: {}
[21.164805] (-) TimerEvent: {}
[21.265054] (-) TimerEvent: {}
[21.365299] (-) TimerEvent: {}
[21.465555] (-) TimerEvent: {}
[21.566563] (-) TimerEvent: {}
[21.666801] (-) TimerEvent: {}
[21.701995] (sl_vcu_all) StdoutLine: {'line': b'[ 80%] \x1b[32m\x1b[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[21.766907] (-) TimerEvent: {}
[21.825851] (sl_vcu_all) StdoutLine: {'line': b'[ 80%] Built target sl_vcu_all__rosidl_typesupport_introspection_cpp\n'}
[21.853545] (sl_vcu_all) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding CXX object CMakeFiles/bumper_sensor_node.dir/src/bumper_sensor_node.cpp.o\x1b[0m\n'}
[21.867003] (-) TimerEvent: {}
[21.967250] (-) TimerEvent: {}
[22.067556] (-) TimerEvent: {}
[22.168556] (-) TimerEvent: {}
[22.268800] (-) TimerEvent: {}
[22.369047] (-) TimerEvent: {}
[22.469298] (-) TimerEvent: {}
[22.569551] (-) TimerEvent: {}
[22.670560] (-) TimerEvent: {}
[22.770798] (-) TimerEvent: {}
[22.871033] (-) TimerEvent: {}
[22.971280] (-) TimerEvent: {}
[23.071543] (-) TimerEvent: {}
[23.172561] (-) TimerEvent: {}
[23.272801] (-) TimerEvent: {}
[23.373046] (-) TimerEvent: {}
[23.473289] (-) TimerEvent: {}
[23.573539] (-) TimerEvent: {}
[23.674560] (-) TimerEvent: {}
[23.774806] (-) TimerEvent: {}
[23.875053] (-) TimerEvent: {}
[23.975295] (-) TimerEvent: {}
[24.075557] (-) TimerEvent: {}
[24.176686] (-) TimerEvent: {}
[24.276948] (-) TimerEvent: {}
[24.377211] (-) TimerEvent: {}
[24.477483] (-) TimerEvent: {}
[24.578560] (-) TimerEvent: {}
[24.678803] (-) TimerEvent: {}
[24.779045] (-) TimerEvent: {}
[24.879292] (-) TimerEvent: {}
[24.979543] (-) TimerEvent: {}
[25.081700] (-) TimerEvent: {}
[25.182137] (-) TimerEvent: {}
[25.282379] (-) TimerEvent: {}
[25.382629] (-) TimerEvent: {}
[25.482879] (-) TimerEvent: {}
[25.583556] (-) TimerEvent: {}
[25.683796] (-) TimerEvent: {}
[25.784047] (-) TimerEvent: {}
[25.885660] (-) TimerEvent: {}
[25.985895] (-) TimerEvent: {}
[26.086132] (-) TimerEvent: {}
[26.186369] (-) TimerEvent: {}
[26.286636] (-) TimerEvent: {}
[26.386894] (-) TimerEvent: {}
[26.487158] (-) TimerEvent: {}
[26.588698] (-) TimerEvent: {}
[26.688956] (-) TimerEvent: {}
[26.789210] (-) TimerEvent: {}
[26.889446] (-) TimerEvent: {}
[26.989694] (-) TimerEvent: {}
[27.089942] (-) TimerEvent: {}
[27.191674] (-) TimerEvent: {}
[27.291926] (-) TimerEvent: {}
[27.392180] (-) TimerEvent: {}
[27.492425] (-) TimerEvent: {}
[27.595661] (-) TimerEvent: {}
[27.695913] (-) TimerEvent: {}
[27.796165] (-) TimerEvent: {}
[27.803753] (sl_vcu_all) StdoutLine: {'line': b'[ 80%] \x1b[32m\x1b[1mLinking CXX executable can_frame_dispatcher_node\x1b[0m\n'}
[27.896272] (-) TimerEvent: {}
[27.996547] (-) TimerEvent: {}
[28.096808] (-) TimerEvent: {}
[28.197044] (-) TimerEvent: {}
[28.261885] (sl_vcu_all) StdoutLine: {'line': b'[ 80%] Built target can_frame_dispatcher_node\n'}
[28.289362] (sl_vcu_all) StdoutLine: {'line': b'[ 81%] \x1b[32mBuilding CXX object CMakeFiles/imu_sensor_node.dir/src/imu_sensor_node.cpp.o\x1b[0m\n'}
[28.297143] (-) TimerEvent: {}
[28.397378] (-) TimerEvent: {}
[28.497616] (-) TimerEvent: {}
[28.597865] (-) TimerEvent: {}
[28.698559] (-) TimerEvent: {}
[28.798827] (-) TimerEvent: {}
[28.899561] (-) TimerEvent: {}
[28.999819] (-) TimerEvent: {}
[29.100079] (-) TimerEvent: {}
[29.203695] (-) TimerEvent: {}
[29.304127] (-) TimerEvent: {}
[29.404562] (-) TimerEvent: {}
[29.504983] (-) TimerEvent: {}
[29.605407] (-) TimerEvent: {}
[29.705839] (-) TimerEvent: {}
[29.806265] (-) TimerEvent: {}
[29.906694] (-) TimerEvent: {}
[30.007131] (-) TimerEvent: {}
[30.107566] (-) TimerEvent: {}
[30.207805] (-) TimerEvent: {}
[30.308060] (-) TimerEvent: {}
[30.410698] (-) TimerEvent: {}
[30.510951] (-) TimerEvent: {}
[30.611208] (-) TimerEvent: {}
[30.711464] (-) TimerEvent: {}
[30.811734] (-) TimerEvent: {}
[30.911995] (-) TimerEvent: {}
[31.012257] (-) TimerEvent: {}
[31.112536] (-) TimerEvent: {}
[31.212809] (-) TimerEvent: {}
[31.313077] (-) TimerEvent: {}
[31.413344] (-) TimerEvent: {}
[31.514696] (-) TimerEvent: {}
[31.614960] (-) TimerEvent: {}
[31.715231] (-) TimerEvent: {}
[31.815484] (-) TimerEvent: {}
[31.915749] (-) TimerEvent: {}
[32.016653] (-) TimerEvent: {}
[32.116904] (-) TimerEvent: {}
[32.194343] (sl_vcu_all) StdoutLine: {'line': b'[ 82%] \x1b[32m\x1b[1mLinking CXX executable bumper_sensor_node\x1b[0m\n'}
[32.217562] (-) TimerEvent: {}
[32.318010] (-) TimerEvent: {}
[32.418293] (-) TimerEvent: {}
[32.519691] (-) TimerEvent: {}
[32.616973] (sl_vcu_all) StdoutLine: {'line': b'[ 82%] Built target bumper_sensor_node\n'}
[32.619782] (-) TimerEvent: {}
[32.645429] (sl_vcu_all) StdoutLine: {'line': b'[ 82%] \x1b[32mBuilding CXX object CMakeFiles/teleop_key.dir/src/teleop_key.cpp.o\x1b[0m\n'}
[32.719881] (-) TimerEvent: {}
[32.820307] (-) TimerEvent: {}
[32.920744] (-) TimerEvent: {}
[33.020993] (-) TimerEvent: {}
[33.121245] (-) TimerEvent: {}
[33.221492] (-) TimerEvent: {}
[33.321748] (-) TimerEvent: {}
[33.421996] (-) TimerEvent: {}
[33.522242] (-) TimerEvent: {}
[33.622490] (-) TimerEvent: {}
[33.725667] (-) TimerEvent: {}
[33.825928] (-) TimerEvent: {}
[33.926170] (-) TimerEvent: {}
[34.026410] (-) TimerEvent: {}
[34.126657] (-) TimerEvent: {}
[34.227000] (-) TimerEvent: {}
[34.327267] (-) TimerEvent: {}
[34.427532] (-) TimerEvent: {}
[34.527942] (-) TimerEvent: {}
[34.628362] (-) TimerEvent: {}
[34.728784] (-) TimerEvent: {}
[34.831689] (-) TimerEvent: {}
[34.931946] (-) TimerEvent: {}
[35.032395] (-) TimerEvent: {}
[35.132858] (-) TimerEvent: {}
[35.233278] (-) TimerEvent: {}
[35.333714] (-) TimerEvent: {}
[35.434132] (-) TimerEvent: {}
[35.535556] (-) TimerEvent: {}
[35.635807] (-) TimerEvent: {}
[35.736059] (-) TimerEvent: {}
[35.836556] (-) TimerEvent: {}
[35.938692] (-) TimerEvent: {}
[36.038965] (-) TimerEvent: {}
[36.139387] (-) TimerEvent: {}
[36.239818] (-) TimerEvent: {}
[36.340062] (-) TimerEvent: {}
[36.440305] (-) TimerEvent: {}
[36.540565] (-) TimerEvent: {}
[36.643695] (-) TimerEvent: {}
[36.743948] (-) TimerEvent: {}
[36.844199] (-) TimerEvent: {}
[36.944452] (-) TimerEvent: {}
[37.044709] (-) TimerEvent: {}
[37.144965] (-) TimerEvent: {}
[37.245217] (-) TimerEvent: {}
[37.345471] (-) TimerEvent: {}
[37.445732] (-) TimerEvent: {}
[37.546088] (-) TimerEvent: {}
[37.646501] (-) TimerEvent: {}
[37.747677] (-) TimerEvent: {}
[37.848085] (-) TimerEvent: {}
[37.956566] (-) TimerEvent: {}
[38.057665] (-) TimerEvent: {}
[38.067901] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] \x1b[32m\x1b[1mLinking CXX executable teleop_key\x1b[0m\n'}
[38.158545] (-) TimerEvent: {}
[38.263713] (-) TimerEvent: {}
[38.364110] (-) TimerEvent: {}
[38.429600] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] Built target teleop_key\n'}
[38.458541] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/battery_monitor_node.dir/src/battery_monitor_node.cpp.o\x1b[0m\n'}
[38.464209] (-) TimerEvent: {}
[38.564560] (-) TimerEvent: {}
[38.664807] (-) TimerEvent: {}
[38.765058] (-) TimerEvent: {}
[38.867688] (-) TimerEvent: {}
[38.967943] (-) TimerEvent: {}
[39.068183] (-) TimerEvent: {}
[39.168424] (-) TimerEvent: {}
[39.268671] (-) TimerEvent: {}
[39.368932] (-) TimerEvent: {}
[39.469187] (-) TimerEvent: {}
[39.569439] (-) TimerEvent: {}
[39.669686] (-) TimerEvent: {}
[39.769926] (-) TimerEvent: {}
[39.870186] (-) TimerEvent: {}
[39.970440] (-) TimerEvent: {}
[40.072705] (-) TimerEvent: {}
[40.142568] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] \x1b[32m\x1b[1mLinking CXX executable imu_sensor_node\x1b[0m\n'}
[40.172806] (-) TimerEvent: {}
[40.273081] (-) TimerEvent: {}
[40.373346] (-) TimerEvent: {}
[40.473642] (-) TimerEvent: {}
[40.573935] (-) TimerEvent: {}
[40.674182] (-) TimerEvent: {}
[40.679538] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] Built target imu_sensor_node\n'}
[40.707042] (sl_vcu_all) StdoutLine: {'line': b'[ 84%] \x1b[32mBuilding CXX object CMakeFiles/jack_control_node.dir/src/jack_control_node.cpp.o\x1b[0m\n'}
[40.774554] (-) TimerEvent: {}
[40.874789] (-) TimerEvent: {}
[40.977554] (-) TimerEvent: {}
[41.077813] (-) TimerEvent: {}
[41.178046] (-) TimerEvent: {}
[41.278279] (-) TimerEvent: {}
[41.378523] (-) TimerEvent: {}
[41.478767] (-) TimerEvent: {}
[41.579011] (-) TimerEvent: {}
[41.679258] (-) TimerEvent: {}
[41.779513] (-) TimerEvent: {}
[41.879763] (-) TimerEvent: {}
[41.980017] (-) TimerEvent: {}
[42.080462] (-) TimerEvent: {}
[42.180891] (-) TimerEvent: {}
[42.283700] (-) TimerEvent: {}
[42.326602] (sl_vcu_all) StdoutLine: {'line': b'[ 85%] \x1b[32m\x1b[1mLinking CXX executable zl_motor_modbus_controller_node\x1b[0m\n'}
[42.383802] (-) TimerEvent: {}
[42.484056] (-) TimerEvent: {}
[42.584323] (-) TimerEvent: {}
[42.684577] (-) TimerEvent: {}
[42.784872] (-) TimerEvent: {}
[42.887732] (-) TimerEvent: {}
[42.988032] (-) TimerEvent: {}
[43.088319] (-) TimerEvent: {}
[43.188575] (-) TimerEvent: {}
[43.288845] (-) TimerEvent: {}
[43.367792] (sl_vcu_all) StdoutLine: {'line': b'[ 85%] Built target zl_motor_modbus_controller_node\n'}
[43.388947] (-) TimerEvent: {}
[43.396344] (sl_vcu_all) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding CXX object CMakeFiles/led_display_control_node.dir/src/led_display_control_node.cpp.o\x1b[0m\n'}
[43.489050] (-) TimerEvent: {}
[43.589290] (-) TimerEvent: {}
[43.689534] (-) TimerEvent: {}
[43.749333] (sl_vcu_all) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/src/jack_control_node.cpp:1\x1b[m\x1b[K:\n'}
[43.750789] (sl_vcu_all) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include/sl_vcu_all/jack_control_node.hpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[Ksl_vcu_all::JackControlNode::\x1b[01;32m\x1b[KJackControlNode\x1b[m\x1b[K(const rclcpp::NodeOptions&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[43.751207] (sl_vcu_all) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include/sl_vcu_all/jack_control_node.hpp:263:10:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Ksl_vcu_all::JackControlNode::response_received_\x1b[m\x1b[K\xe2\x80\x99 will be initialized after [\x1b[01;35m\x1b[K-Wreorder\x1b[m\x1b[K]\n'}
[43.751308] (sl_vcu_all) StderrLine: {'line': b'  263 |     bool \x1b[01;35m\x1b[Kresponse_received_\x1b[m\x1b[K;\n'}
[43.751391] (sl_vcu_all) StderrLine: {'line': b'      |          \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[43.751472] (sl_vcu_all) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include/sl_vcu_all/jack_control_node.hpp:226:43:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  \xe2\x80\x98\x1b[01m\x1b[Kstd::chrono::_V2::steady_clock::time_point sl_vcu_all::JackControlNode::last_send_time_\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K-Wreorder\x1b[m\x1b[K]\n'}
[43.751568] (sl_vcu_all) StderrLine: {'line': b'  226 |     std::chrono::steady_clock::time_point \x1b[01;35m\x1b[Klast_send_time_\x1b[m\x1b[K;\n'}
[43.751650] (sl_vcu_all) StderrLine: {'line': b'      |                                           \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[43.751726] (sl_vcu_all) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/src/jack_control_node.cpp:8:1:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  when initialized here [\x1b[01;35m\x1b[K-Wreorder\x1b[m\x1b[K]\n'}
[43.751813] (sl_vcu_all) StderrLine: {'line': b'    8 | \x1b[01;35m\x1b[KJackControlNode\x1b[m\x1b[K::JackControlNode(const rclcpp::NodeOptions & options)\n'}
[43.751890] (sl_vcu_all) StderrLine: {'line': b'      | \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[43.790557] (-) TimerEvent: {}
[43.890797] (-) TimerEvent: {}
[43.991040] (-) TimerEvent: {}
[44.091280] (-) TimerEvent: {}
[44.191524] (-) TimerEvent: {}
[44.292571] (-) TimerEvent: {}
[44.392814] (-) TimerEvent: {}
[44.493073] (-) TimerEvent: {}
[44.593334] (-) TimerEvent: {}
[44.693559] (-) TimerEvent: {}
[44.794559] (-) TimerEvent: {}
[44.894800] (-) TimerEvent: {}
[44.995048] (-) TimerEvent: {}
[45.095496] (-) TimerEvent: {}
[45.196558] (-) TimerEvent: {}
[45.296805] (-) TimerEvent: {}
[45.397049] (-) TimerEvent: {}
[45.409657] (sl_vcu_all) StdoutLine: {'line': b'[ 86%] \x1b[32m\x1b[1mLinking CXX executable battery_monitor_node\x1b[0m\n'}
[45.497158] (-) TimerEvent: {}
[45.597421] (-) TimerEvent: {}
[45.697548] (sl_vcu_all) StdoutLine: {'line': b'[ 86%] Built target battery_monitor_node\n'}
[45.697740] (-) TimerEvent: {}
[45.725030] (sl_vcu_all) StdoutLine: {'line': b'[ 86%] Built target sl_vcu_all\n'}
[45.753393] (sl_vcu_all) StdoutLine: {'line': b'[ 86%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[45.797880] (-) TimerEvent: {}
[45.898115] (-) TimerEvent: {}
[45.998357] (-) TimerEvent: {}
[46.099668] (-) TimerEvent: {}
[46.199900] (-) TimerEvent: {}
[46.300120] (-) TimerEvent: {}
[46.400355] (-) TimerEvent: {}
[46.501553] (-) TimerEvent: {}
[46.601784] (-) TimerEvent: {}
[46.702006] (-) TimerEvent: {}
[46.802232] (-) TimerEvent: {}
[46.902465] (-) TimerEvent: {}
[47.002893] (-) TimerEvent: {}
[47.105644] (-) TimerEvent: {}
[47.205880] (-) TimerEvent: {}
[47.306100] (-) TimerEvent: {}
[47.406322] (-) TimerEvent: {}
[47.506549] (-) TimerEvent: {}
[47.606771] (-) TimerEvent: {}
[47.706978] (-) TimerEvent: {}
[47.807193] (-) TimerEvent: {}
[47.907406] (-) TimerEvent: {}
[48.007628] (-) TimerEvent: {}
[48.096523] (sl_vcu_all) StdoutLine: {'line': b'[ 86%] \x1b[32m\x1b[1mLinking CXX executable zl_motor_controller_node\x1b[0m\n'}
[48.107721] (-) TimerEvent: {}
[48.208158] (-) TimerEvent: {}
[48.308517] (-) TimerEvent: {}
[48.409575] (-) TimerEvent: {}
[48.415518] (sl_vcu_all) StdoutLine: {'line': b'[ 87%] \x1b[32m\x1b[1mLinking CXX executable jack_control_node\x1b[0m\n'}
[48.509687] (-) TimerEvent: {}
[48.610558] (-) TimerEvent: {}
[48.710802] (-) TimerEvent: {}
[48.762361] (sl_vcu_all) StdoutLine: {'line': b'[ 87%] Built target jack_control_node\n'}
[48.810945] (-) TimerEvent: {}
[48.911391] (-) TimerEvent: {}
[49.013713] (-) TimerEvent: {}
[49.096080] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] \x1b[32m\x1b[1mLinking CXX executable led_display_control_node\x1b[0m\n'}
[49.113819] (-) TimerEvent: {}
[49.214079] (-) TimerEvent: {}
[49.314608] (-) TimerEvent: {}
[49.318179] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target led_display_control_node\n'}
[49.415328] (-) TimerEvent: {}
[49.453765] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target sl_vcu_all__py\n'}
[49.482001] (sl_vcu_all) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/srv/_add_can_filter_s.c.o\x1b[0m\n'}
[49.482211] (sl_vcu_all) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/srv/_led_control_s.c.o\x1b[0m\n'}
[49.482315] (sl_vcu_all) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/srv/_check_node_status_s.c.o\x1b[0m\n'}
[49.507723] (sl_vcu_all) StdoutLine: {'line': b'[ 89%] Built target zl_motor_controller_node\n'}
[49.515421] (-) TimerEvent: {}
[49.519076] (sl_vcu_all) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_motor_info_s.c.o\x1b[0m\n'}
[49.615521] (-) TimerEvent: {}
[49.690535] (sl_vcu_all) StdoutLine: {'line': b'[ 91%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_motor_state_s.c.o\x1b[0m\n'}
[49.694103] (sl_vcu_all) StdoutLine: {'line': b'[ 91%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_bumper_state_s.c.o\x1b[0m\n'}
[49.697238] (sl_vcu_all) StdoutLine: {'line': b'[ 92%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_can_frame_s.c.o\x1b[0m\n'}
[49.715608] (-) TimerEvent: {}
[49.732566] (sl_vcu_all) StdoutLine: {'line': b'[ 93%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_battery_status_s.c.o\x1b[0m\n'}
[49.815712] (-) TimerEvent: {}
[49.853464] (sl_vcu_all) StdoutLine: {'line': b'[ 93%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_part_data_s.c.o\x1b[0m\n'}
[49.855109] (sl_vcu_all) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_channel_data_s.c.o\x1b[0m\n'}
[49.866374] (sl_vcu_all) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_jack_status_s.c.o\x1b[0m\n'}
[49.893742] (sl_vcu_all) StdoutLine: {'line': b'[ 95%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/action/_jack_control_s.c.o\x1b[0m\n'}
[49.915810] (-) TimerEvent: {}
[50.016259] (-) TimerEvent: {}
[50.116538] (-) TimerEvent: {}
[50.151093] (sl_vcu_all) StdoutLine: {'line': b'[ 96%] \x1b[32m\x1b[1mLinking C shared library libsl_vcu_all__rosidl_generator_py.so\x1b[0m\n'}
[50.209869] (sl_vcu_all) StdoutLine: {'line': b'[ 96%] Built target sl_vcu_all__rosidl_generator_py\n'}
[50.216585] (-) TimerEvent: {}
[50.236280] (sl_vcu_all) StdoutLine: {'line': b'[ 98%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all_s__rosidl_typesupport_introspection_c.dir/rosidl_generator_py/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_introspection_c.c.o\x1b[0m\n'}
[50.236462] (sl_vcu_all) StdoutLine: {'line': b'[ 98%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all_s__rosidl_typesupport_c.dir/rosidl_generator_py/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_c.c.o\x1b[0m\n'}
[50.236575] (sl_vcu_all) StdoutLine: {'line': b'[ 98%] \x1b[32mBuilding C object CMakeFiles/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.dir/rosidl_generator_py/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c.o\x1b[0m\n'}
[50.316684] (-) TimerEvent: {}
[50.416912] (-) TimerEvent: {}
[50.459912] (sl_vcu_all) StdoutLine: {'line': b'[ 98%] \x1b[32m\x1b[1mLinking C shared module rosidl_generator_py/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\x1b[0m\n'}
[50.461685] (sl_vcu_all) StdoutLine: {'line': b'[ 99%] \x1b[32m\x1b[1mLinking C shared module rosidl_generator_py/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[50.468540] (sl_vcu_all) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared module rosidl_generator_py/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[50.503097] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target sl_vcu_all_s__rosidl_typesupport_c\n'}
[50.505801] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target sl_vcu_all_s__rosidl_typesupport_introspection_c\n'}
[50.514118] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target sl_vcu_all_s__rosidl_typesupport_fastrtps_c\n'}
[50.517020] (-) TimerEvent: {}
[50.529889] (sl_vcu_all) CommandEnded: {'returncode': 0}
[50.530805] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'install'}
[50.542024] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 60526 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', 'SSH_TTY': '/dev/pts/1', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '4101', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 60526 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[50.552824] (sl_vcu_all) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[50.553125] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/rosidl_interfaces/sl_vcu_all\n'}
[50.553378] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.json\n'}
[50.553640] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.json\n'}
[50.553925] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.json\n'}
[50.554126] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.json\n'}
[50.554324] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.json\n'}
[50.554499] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.json\n'}
[50.554737] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.json\n'}
[50.554927] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.json\n'}
[50.555059] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.json\n'}
[50.555229] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.json\n'}
[50.555407] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.json\n'}
[50.555731] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.json\n'}
[50.555940] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[50.556098] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[50.556331] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[50.556435] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.h\n'}
[50.556561] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.h\n'}
[50.556782] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.h\n'}
[50.556923] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.c\n'}
[50.557161] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[50.557343] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__description.c\n'}
[50.557488] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.h\n'}
[50.557708] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[50.557927] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.h\n'}
[50.558086] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[50.558216] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.h\n'}
[50.558328] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.h\n'}
[50.558430] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.c\n'}
[50.558724] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.h\n'}
[50.558864] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[50.558985] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[50.559146] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.h\n'}
[50.559321] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__description.c\n'}
[50.559459] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.h\n'}
[50.559633] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.c\n'}
[50.559798] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[50.559945] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.c\n'}
[50.560124] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__description.c\n'}
[50.560283] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__description.c\n'}
[50.560413] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.h\n'}
[50.560609] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.h\n'}
[50.560768] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.h\n'}
[50.560999] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.h\n'}
[50.561187] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.h\n'}
[50.561339] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.h\n'}
[50.561452] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[50.561574] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.h\n'}
[50.561722] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.h\n'}
[50.561855] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.h\n'}
[50.562004] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.h\n'}
[50.562166] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_c__visibility_control.h\n'}
[50.562337] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.h\n'}
[50.562484] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[50.562669] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.h\n'}
[50.562841] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__description.c\n'}
[50.563016] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[50.563166] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__description.c\n'}
[50.563310] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.c\n'}
[50.563459] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.c\n'}
[50.563665] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.h\n'}
[50.563804] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[50.563953] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.h\n'}
[50.564081] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[50.564271] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.h\n'}
[50.564429] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.h\n'}
[50.564597] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.h\n'}
[50.564773] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.c\n'}
[50.564948] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.h\n'}
[50.565117] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[50.565253] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[50.565384] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.h\n'}
[50.565515] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[50.565709] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__description.c\n'}
[50.565879] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__description.c\n'}
[50.566049] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.h\n'}
[50.566220] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.h\n'}
[50.566376] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.h\n'}
[50.566486] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.c\n'}
[50.566654] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.h\n'}
[50.566787] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.h\n'}
[50.566942] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.h\n'}
[50.567124] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.h\n'}
[50.567295] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[50.567450] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[50.567609] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__description.c\n'}
[50.567762] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.h\n'}
[50.567896] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.c\n'}
[50.568054] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.h\n'}
[50.568202] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.c\n'}
[50.568358] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__description.c\n'}
[50.568498] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.h\n'}
[50.568694] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__description.c\n'}
[50.568861] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.c\n'}
[50.569008] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__description.c\n'}
[50.569177] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.h\n'}
[50.569358] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.h\n'}
[50.569486] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.h\n'}
[50.569691] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.h\n'}
[50.569864] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.c\n'}
[50.570025] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.h\n'}
[50.570168] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.h\n'}
[50.570318] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.h\n'}
[50.570466] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.h\n'}
[50.570631] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.h\n'}
[50.570851] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.sh\n'}
[50.571259] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.dsv\n'}
[50.571509] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so\n'}
[50.572262] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so" to ""\n'}
[50.572420] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[50.572535] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[50.572622] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[50.572737] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_c.h\n'}
[50.572850] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[50.572961] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[50.573071] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_c.h\n'}
[50.573182] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_c.h\n'}
[50.573323] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_c.h\n'}
[50.573441] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[50.573548] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[50.573651] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_c.h\n'}
[50.573735] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_c.h\n'}
[50.573885] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_c.h\n'}
[50.574057] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_c.h\n'}
[50.574178] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_c.h\n'}
[50.574276] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_c.h\n'}
[50.574362] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_c.h\n'}
[50.574462] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_c.h\n'}
[50.574606] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[50.574781] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so\n'}
[50.575454] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[50.575620] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[50.575721] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[50.575836] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[50.575934] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__builder.hpp\n'}
[50.576033] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__traits.hpp\n'}
[50.576124] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.hpp\n'}
[50.576233] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.hpp\n'}
[50.576344] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.hpp\n'}
[50.576474] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[50.576587] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.hpp\n'}
[50.576701] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[50.576787] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.hpp\n'}
[50.576897] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__builder.hpp\n'}
[50.577012] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__builder.hpp\n'}
[50.577123] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.hpp\n'}
[50.577249] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__builder.hpp\n'}
[50.577399] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.hpp\n'}
[50.577534] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__traits.hpp\n'}
[50.577710] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__traits.hpp\n'}
[50.577870] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.hpp\n'}
[50.578021] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__traits.hpp\n'}
[50.578176] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.hpp\n'}
[50.578336] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.hpp\n'}
[50.578495] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.hpp\n'}
[50.578649] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.hpp\n'}
[50.578756] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[50.578858] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.hpp\n'}
[50.578971] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.hpp\n'}
[50.579091] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.hpp\n'}
[50.579225] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.hpp\n'}
[50.579365] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[50.579472] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__traits.hpp\n'}
[50.579610] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.hpp\n'}
[50.579723] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__builder.hpp\n'}
[50.579885] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__traits.hpp\n'}
[50.580040] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__traits.hpp\n'}
[50.580197] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.hpp\n'}
[50.580355] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__builder.hpp\n'}
[50.580483] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__traits.hpp\n'}
[50.580658] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__builder.hpp\n'}
[50.580816] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.hpp\n'}
[50.580971] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__traits.hpp\n'}
[50.581098] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__traits.hpp\n'}
[50.581233] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__builder.hpp\n'}
[50.581364] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__builder.hpp\n'}
[50.581497] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__traits.hpp\n'}
[50.581651] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.hpp\n'}
[50.581783] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.hpp\n'}
[50.581947] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.hpp\n'}
[50.582104] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.hpp\n'}
[50.582232] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.hpp\n'}
[50.582369] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.hpp\n'}
[50.582510] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__builder.hpp\n'}
[50.582648] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.hpp\n'}
[50.582786] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__traits.hpp\n'}
[50.582921] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.hpp\n'}
[50.583549] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.hpp\n'}
[50.583737] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.hpp\n'}
[50.583895] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.hpp\n'}
[50.584058] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.hpp\n'}
[50.584183] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__builder.hpp\n'}
[50.584317] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__builder.hpp\n'}
[50.584456] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.hpp\n'}
[50.584607] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.hpp\n'}
[50.584731] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.hpp\n'}
[50.584854] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.hpp\n'}
[50.584981] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[50.585105] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.hpp\n'}
[50.585272] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[50.585425] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[50.585516] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[50.585626] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/dds_fastrtps\n'}
[50.585747] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.585866] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[50.585968] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[50.586065] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/dds_fastrtps\n'}
[50.586147] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.586290] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.586450] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.586561] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[50.586650] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[50.586765] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/dds_fastrtps\n'}
[50.586848] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.586947] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.587049] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.587132] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.587260] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.587362] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.587462] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.587627] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[50.587787] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[50.587994] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so\n'}
[50.589101] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so" to ""\n'}
[50.589265] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[50.589416] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[50.589524] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[50.589629] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_c.h\n'}
[50.589707] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[50.589830] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[50.589939] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[50.590041] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_c.h\n'}
[50.590127] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[50.590221] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[50.590317] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_c.h\n'}
[50.590444] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[50.590621] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_c.h\n'}
[50.590744] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[50.590850] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[50.590996] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[50.591092] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[50.591235] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_c.h\n'}
[50.591367] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_c.h\n'}
[50.591481] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[50.591660] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[50.591825] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_c.h\n'}
[50.591986] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_c.h\n'}
[50.592142] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[50.592309] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[50.592476] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[50.592623] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[50.592763] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[50.592914] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_c.h\n'}
[50.593052] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_c.h\n'}
[50.593189] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_c.h\n'}
[50.593328] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_c.h\n'}
[50.593655] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so\n'}
[50.594229] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so" to ""\n'}
[50.594362] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so\n'}
[50.594938] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so" to ""\n'}
[50.595143] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[50.595257] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[50.595345] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[50.595442] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.595581] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.cpp\n'}
[50.595671] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[50.595811] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[50.595923] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.cpp\n'}
[50.596038] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.cpp\n'}
[50.596166] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.596266] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.cpp\n'}
[50.596366] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.596443] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.596888] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[50.597140] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[50.597313] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.cpp\n'}
[50.597434] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.597558] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.cpp\n'}
[50.597645] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.cpp\n'}
[50.597748] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.cpp\n'}
[50.597850] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.598039] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.598174] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.598304] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.598457] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.598638] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.cpp\n'}
[50.598803] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.cpp\n'}
[50.598964] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.599121] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.cpp\n'}
[50.599292] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[50.599444] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.cpp\n'}
[50.599685] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so\n'}
[50.600757] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so" to ""\n'}
[50.600984] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so\n'}
[50.601762] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so" to ""\n'}
[50.601973] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.sh\n'}
[50.602241] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.dsv\n'}
[50.602393] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info\n'}
[50.602594] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/SOURCES.txt\n'}
[50.603068] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/PKG-INFO\n'}
[50.603236] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/top_level.txt\n'}
[50.603388] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/dependency_links.txt\n'}
[50.603516] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all\n'}
[50.603712] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_introspection_c.c\n'}
[50.603956] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[50.604118] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_c.c\n'}
[50.604363] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action\n'}
[50.604473] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/__init__.py\n'}
[50.604623] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control.py\n'}
[50.604825] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control_s.c\n'}
[50.605011] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[50.605287] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/__init__.py\n'}
[50.605405] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv\n'}
[50.605513] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status_s.c\n'}
[50.605660] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/__init__.py\n'}
[50.605781] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control.py\n'}
[50.605907] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control_s.c\n'}
[50.606083] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status.py\n'}
[50.606241] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter.py\n'}
[50.606408] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter_s.c\n'}
[50.606590] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[50.606783] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg\n'}
[50.606962] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info.py\n'}
[50.607070] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame_s.c\n'}
[50.607178] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data_s.c\n'}
[50.607303] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info_s.c\n'}
[50.607428] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data.py\n'}
[50.607665] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status_s.c\n'}
[50.607832] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status.py\n'}
[50.607972] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state_s.c\n'}
[50.608093] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/__init__.py\n'}
[50.608229] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status_s.c\n'}
[50.608361] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state.py\n'}
[50.608480] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status.py\n'}
[50.608652] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame.py\n'}
[50.608810] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state.py\n'}
[50.608965] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data.py\n'}
[50.609116] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state_s.c\n'}
[50.609234] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data_s.c\n'}
[50.609365] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[50.617102] (-) TimerEvent: {}
[50.689335] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all'...\n"}
[50.689500] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/__init__.py'...\n"}
[50.689644] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action'...\n"}
[50.689723] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/__init__.py'...\n"}
[50.689797] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control.py'...\n"}
[50.689868] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg'...\n"}
[50.689938] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/__init__.py'...\n"}
[50.690007] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status.py'...\n"}
[50.690077] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state.py'...\n"}
[50.690148] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame.py'...\n"}
[50.690219] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data.py'...\n"}
[50.690296] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status.py'...\n"}
[50.690366] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info.py'...\n"}
[50.690436] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state.py'...\n"}
[50.690529] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data.py'...\n"}
[50.690657] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv'...\n"}
[50.690727] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/__init__.py'...\n"}
[50.690797] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter.py'...\n"}
[50.690873] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status.py'...\n"}
[50.690943] (sl_vcu_all) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control.py'...\n"}
[50.696208] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[50.696792] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[50.697173] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[50.697785] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so" to ""\n'}
[50.698036] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[50.698580] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so" to ""\n'}
[50.698902] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so\n'}
[50.699514] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so" to ""\n'}
[50.699850] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.idl\n'}
[50.700032] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.idl\n'}
[50.700159] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.idl\n'}
[50.700298] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.idl\n'}
[50.700440] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.idl\n'}
[50.700709] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.idl\n'}
[50.700919] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.idl\n'}
[50.701139] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.idl\n'}
[50.701337] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.idl\n'}
[50.701530] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.idl\n'}
[50.701815] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.idl\n'}
[50.702082] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.idl\n'}
[50.702277] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.srv\n'}
[50.702816] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.srv\n'}
[50.703224] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.srv\n'}
[50.703661] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.msg\n'}
[50.704109] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.msg\n'}
[50.704562] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.msg\n'}
[50.705204] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.msg\n'}
[50.705636] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.msg\n'}
[50.706043] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.msg\n'}
[50.706401] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.msg\n'}
[50.706835] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.msg\n'}
[50.707250] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.action\n'}
[50.707730] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/can_frame_dispatcher_node\n'}
[50.712932] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/can_frame_dispatcher_node" to ""\n'}
[50.713310] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_controller_node\n'}
[50.717218] (-) TimerEvent: {}
[50.728286] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_controller_node" to ""\n'}
[50.728534] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_modbus_controller_node\n'}
[50.739767] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_modbus_controller_node" to ""\n'}
[50.740140] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/bumper_sensor_node\n'}
[50.744291] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/bumper_sensor_node" to ""\n'}
[50.744501] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/imu_sensor_node\n'}
[50.749284] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/imu_sensor_node" to ""\n'}
[50.749568] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/teleop_key\n'}
[50.750817] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/teleop_key" to ""\n'}
[50.751116] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/battery_monitor_node\n'}
[50.752966] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/battery_monitor_node" to ""\n'}
[50.753304] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/jack_control_node\n'}
[50.755324] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/jack_control_node" to ""\n'}
[50.755532] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/led_display_control_node\n'}
[50.756991] (sl_vcu_all) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/led_display_control_node" to ""\n'}
[50.757332] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch\n'}
[50.757450] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/battery_monitor.launch.py\n'}
[50.757745] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/jack_control.launch.py\n'}
[50.758105] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/robot_localization_ekf.launch.py\n'}
[50.758480] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_controller.launch.py\n'}
[50.758877] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_modbus_controller.launch.py\n'}
[50.759234] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/led_control.launch.py\n'}
[50.759511] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/vcu_nodes.launch.py\n'}
[50.759782] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/imu_sensor.launch.py\n'}
[50.760133] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/bumper_sensor.launch.py\n'}
[50.760420] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/sl_vcu_all.launch.py\n'}
[50.760838] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config\n'}
[50.761220] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/ekf.yaml\n'}
[50.761443] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_modbus_controller.yaml\n'}
[50.761833] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/battery_monitor.yaml\n'}
[50.762180] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_controller.yaml\n'}
[50.762398] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/jack_control.yaml\n'}
[50.762770] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/imu_sensor.yaml\n'}
[50.763122] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/robot_localization_ekf.yaml\n'}
[50.763524] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/bumper_sensor.yaml\n'}
[50.763913] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/led_control.yaml\n'}
[50.764384] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/package_run_dependencies/sl_vcu_all\n'}
[50.764706] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/parent_prefix_path/sl_vcu_all\n'}
[50.764888] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.sh\n'}
[50.765484] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.dsv\n'}
[50.765730] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.sh\n'}
[50.766304] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.dsv\n'}
[50.766477] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.bash\n'}
[50.766777] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.sh\n'}
[50.766963] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.zsh\n'}
[50.767207] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.dsv\n'}
[50.767380] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv\n'}
[50.767710] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/packages/sl_vcu_all\n'}
[50.767955] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport.cmake\n'}
[50.768209] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport-noconfig.cmake\n'}
[50.768413] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[50.768687] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[50.768983] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cppExport.cmake\n'}
[50.769243] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[50.769424] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[50.769712] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport.cmake\n'}
[50.769927] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[50.770214] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport.cmake\n'}
[50.770388] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport-noconfig.cmake\n'}
[50.770681] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport.cmake\n'}
[50.770849] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[50.771140] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport.cmake\n'}
[50.771321] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[50.771605] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport.cmake\n'}
[50.771795] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport-noconfig.cmake\n'}
[50.772023] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake-extras.cmake\n'}
[50.772267] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[50.772518] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[50.772791] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[50.773023] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_targets-extras.cmake\n'}
[50.773215] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[50.773424] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[50.773664] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig.cmake\n'}
[50.773838] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig-version.cmake\n'}
[50.774048] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.xml\n'}
[50.776752] (sl_vcu_all) CommandEnded: {'returncode': 0}
[50.808121] (sl_vcu_all) JobEnded: {'identifier': 'sl_vcu_all', 'rc': 0}
[50.809051] (-) EventReactorShutdown: {}
