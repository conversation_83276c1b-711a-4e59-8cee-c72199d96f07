[0.013s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all
[0.173s] -- The C compiler identification is GNU 13.3.0
[0.304s] -- The CXX compiler identification is GNU 13.3.0
[0.332s] -- Detecting C compiler ABI info
[0.441s] -- Detecting C compiler ABI info - done
[0.452s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.453s] -- Detecting C compile features
[0.453s] -- Detecting C compile features - done
[0.466s] -- Detecting CXX compiler ABI info
[0.580s] -- Detecting CXX compiler ABI info - done
[0.591s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.591s] -- Detecting CXX compile features
[0.592s] -- Detecting CXX compile features - done
[0.607s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.789s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.963s] -- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
[1.050s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[1.079s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[1.115s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.153s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.301s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[1.309s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[1.438s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
[1.466s] -- Found FastRTPS: /opt/ros/jazzy/include (Required is at least version "2.13") 
[1.494s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.630s] -- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
[1.657s] -- Found rosidl_default_generators: 1.6.0 (/opt/ros/jazzy/share/rosidl_default_generators/cmake)
[1.689s] -- Found rosidl_adapter: 4.6.5 (/opt/ros/jazzy/share/rosidl_adapter/cmake)
[1.698s] -- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
[1.727s] -- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
[1.770s] -- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
[1.780s] -- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
[2.002s] -- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
[2.036s] -- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
[2.038s] -- Found Eigen3: TRUE (found version "3.4.0") 
[2.038s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[2.046s] -- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
[2.083s] -- Found PkgConfig: /usr/bin/pkg-config (found version "1.8.1") 
[2.084s] -- Checking for module 'libmodbus'
[2.108s] --   Found libmodbus, version 3.1.10
[2.151s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[2.947s] -- Found ament_cmake_ros: 0.12.0 (/opt/ros/jazzy/share/ament_cmake_ros/cmake)
[3.843s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[4.189s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[4.197s] -- Found python_cmake_module: 0.11.1 (/opt/ros/jazzy/share/python_cmake_module/cmake)
[4.347s] -- Found PythonExtra: /usr/bin/python3  
[4.824s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[4.987s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[4.988s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include>
[4.988s] -- Configured cppcheck exclude dirs and/or files: 
[4.996s] -- Added test 'flake8' to check Python code syntax and style conventions
[4.996s] -- Configured 'flake8' exclude dirs and/or files: 
[5.012s] -- Added test 'lint_cmake' to check CMake code style
[5.020s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[5.076s] -- Added test 'uncrustify' to check C / C++ code style
[5.076s] -- Configured uncrustify additional arguments: 
[5.085s] -- Added test 'xmllint' to check XML markup files
[5.096s] -- Configuring done (5.1s)
[5.219s] -- Generating done (0.1s)
[5.251s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[5.267s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all
[5.269s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[5.339s] [  0%] [34m[1mGenerating type hashes for ROS interfaces[0m
[5.350s] [  0%] Built target ament_cmake_python_copy_sl_vcu_all
[5.628s] running egg_info
[5.629s] creating sl_vcu_all.egg-info
[5.658s] writing sl_vcu_all.egg-info/PKG-INFO
[5.658s] writing dependency_links to sl_vcu_all.egg-info/dependency_links.txt
[5.659s] writing top-level names to sl_vcu_all.egg-info/top_level.txt
[5.659s] writing manifest file 'sl_vcu_all.egg-info/SOURCES.txt'
[5.743s] reading manifest file 'sl_vcu_all.egg-info/SOURCES.txt'
[5.744s] writing manifest file 'sl_vcu_all.egg-info/SOURCES.txt'
[5.775s] [  0%] Built target ament_cmake_python_build_sl_vcu_all_egg
[6.287s] [  0%] Built target sl_vcu_all__rosidl_generator_type_description
[6.301s] [  1%] [34m[1mGenerating C code for ROS interfaces[0m
[6.315s] [  2%] [34m[1mGenerating C++ code for ROS interfaces[0m
[8.179s] [  2%] Built target sl_vcu_all__cpp
[8.449s] [  4%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/add_can_filter__description.c.o[0m
[8.450s] [  4%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/add_can_filter__type_support.c.o[0m
[8.450s] [  4%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/add_can_filter__functions.c.o[0m
[8.450s] [  4%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/check_node_status__description.c.o[0m
[8.509s] [  5%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/check_node_status__functions.c.o[0m
[8.515s] [  6%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/check_node_status__type_support.c.o[0m
[8.515s] [  6%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/led_control__description.c.o[0m
[8.553s] [  7%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/led_control__functions.c.o[0m
[8.571s] [  7%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/srv/detail/led_control__type_support.c.o[0m
[8.583s] [  8%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_info__description.c.o[0m
[8.602s] [  9%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_info__functions.c.o[0m
[8.622s] [  9%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_info__type_support.c.o[0m
[8.639s] [ 10%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_state__description.c.o[0m
[8.649s] [ 10%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_state__functions.c.o[0m
[8.663s] [ 11%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/motor_state__type_support.c.o[0m
[8.668s] [ 12%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/bumper_state__description.c.o[0m
[8.697s] [ 12%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/bumper_state__functions.c.o[0m
[8.705s] [ 13%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/bumper_state__type_support.c.o[0m
[8.715s] [ 13%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/can_frame__description.c.o[0m
[8.723s] [ 14%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/can_frame__functions.c.o[0m
[8.746s] [ 15%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/can_frame__type_support.c.o[0m
[8.760s] [ 15%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/battery_status__description.c.o[0m
[8.768s] [ 16%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/battery_status__functions.c.o[0m
[8.786s] [ 16%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/part_data__description.c.o[0m
[8.786s] [ 17%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/battery_status__type_support.c.o[0m
[8.813s] [ 18%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/part_data__functions.c.o[0m
[8.826s] [ 18%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/part_data__type_support.c.o[0m
[8.833s] [ 19%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/channel_data__description.c.o[0m
[8.834s] [ 20%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/channel_data__functions.c.o[0m
[8.866s] [ 20%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/channel_data__type_support.c.o[0m
[8.875s] [ 21%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/jack_status__description.c.o[0m
[8.884s] [ 22%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/jack_status__functions.c.o[0m
[8.898s] [ 22%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/msg/detail/jack_status__type_support.c.o[0m
[8.907s] [ 23%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/action/detail/jack_control__description.c.o[0m
[8.928s] [ 23%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/action/detail/jack_control__functions.c.o[0m
[8.938s] [ 24%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_c.dir/rosidl_generator_c/sl_vcu_all/action/detail/jack_control__type_support.c.o[0m
[9.112s] [ 25%] [32m[1mLinking C shared library libsl_vcu_all__rosidl_generator_c.so[0m
[9.165s] [ 25%] Built target sl_vcu_all__rosidl_generator_c
[9.182s] [ 26%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[9.182s] [ 26%] [34m[1mGenerating C introspection for ROS interfaces[0m
[9.182s] [ 26%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[9.185s] [ 27%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[10.542s] [ 27%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/srv/add_can_filter__type_support.cpp.o[0m
[10.944s] [ 28%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/srv/detail/add_can_filter__type_support.c.o[0m
[11.009s] [ 29%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/srv/detail/check_node_status__type_support.c.o[0m
[11.034s] [ 30%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/srv/check_node_status__type_support.cpp.o[0m
[11.071s] [ 30%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/srv/detail/led_control__type_support.c.o[0m
[11.144s] [ 31%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/motor_info__type_support.c.o[0m
[11.197s] [ 32%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/motor_state__type_support.c.o[0m
[11.245s] [ 32%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/bumper_state__type_support.c.o[0m
[11.295s] [ 33%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/can_frame__type_support.c.o[0m
[11.331s] [ 33%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/srv/detail/dds_fastrtps/add_can_filter__type_support.cpp.o[0m
[11.347s] [ 33%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/battery_status__type_support.c.o[0m
[11.401s] [ 34%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/part_data__type_support.c.o[0m
[11.448s] [ 35%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/channel_data__type_support.c.o[0m
[11.483s] [ 36%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/srv/detail/add_can_filter__type_support_c.cpp.o[0m
[11.505s] [ 36%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/msg/detail/jack_status__type_support.c.o[0m
[11.516s] [ 36%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/srv/led_control__type_support.cpp.o[0m
[11.554s] [ 37%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/sl_vcu_all/action/detail/jack_control__type_support.c.o[0m
[11.641s] [ 37%] [32m[1mLinking C shared library libsl_vcu_all__rosidl_typesupport_introspection_c.so[0m
[11.689s] [ 37%] Built target sl_vcu_all__rosidl_typesupport_introspection_c
[11.703s] [ 37%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/srv/detail/check_node_status__type_support_c.cpp.o[0m
[12.097s] [ 38%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/motor_info__type_support.cpp.o[0m
[12.099s] [ 39%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/srv/detail/dds_fastrtps/check_node_status__type_support.cpp.o[0m
[12.104s] [ 40%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[12.319s] [ 41%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/srv/detail/led_control__type_support_c.cpp.o[0m
[12.470s] [ 42%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/motor_state__type_support.cpp.o[0m
[12.842s] [ 43%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/srv/detail/dds_fastrtps/led_control__type_support.cpp.o[0m
[12.848s] [ 43%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/bumper_state__type_support.cpp.o[0m
[12.940s] [ 44%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/motor_info__type_support_c.cpp.o[0m
[13.218s] [ 45%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/can_frame__type_support.cpp.o[0m
[13.504s] [ 45%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/motor_state__type_support_c.cpp.o[0m
[13.518s] [ 45%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/srv/add_can_filter__type_support.cpp.o[0m
[13.581s] [ 46%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/srv/check_node_status__type_support.cpp.o[0m
[13.598s] [ 46%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/battery_status__type_support.cpp.o[0m
[13.644s] [ 47%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/srv/led_control__type_support.cpp.o[0m
[13.706s] [ 47%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/motor_info__type_support.cpp.o[0m
[13.748s] [ 47%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/motor_info__type_support.cpp.o[0m
[13.763s] [ 48%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/motor_state__type_support.cpp.o[0m
[13.819s] [ 49%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/bumper_state__type_support.cpp.o[0m
[13.879s] [ 49%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/can_frame__type_support.cpp.o[0m
[13.932s] [ 50%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/battery_status__type_support.cpp.o[0m
[13.973s] [ 51%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/part_data__type_support.cpp.o[0m
[13.990s] [ 51%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/part_data__type_support.cpp.o[0m
[14.046s] [ 52%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/channel_data__type_support.cpp.o[0m
[14.085s] [ 53%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/bumper_state__type_support_c.cpp.o[0m
[14.102s] [ 54%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/msg/jack_status__type_support.cpp.o[0m
[14.159s] [ 54%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_c.dir/rosidl_typesupport_c/sl_vcu_all/action/jack_control__type_support.cpp.o[0m
[14.236s] [ 55%] [32m[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_c.so[0m
[14.296s] [ 55%] Built target sl_vcu_all__rosidl_typesupport_c
[14.309s] [ 56%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/motor_state__type_support.cpp.o[0m
[14.328s] [ 57%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/channel_data__type_support.cpp.o[0m
[14.349s] [ 58%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[14.643s] [ 58%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/can_frame__type_support_c.cpp.o[0m
[14.702s] [ 58%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/msg/jack_status__type_support.cpp.o[0m
[14.895s] [ 58%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/bumper_state__type_support.cpp.o[0m
[15.081s] [ 59%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/sl_vcu_all/action/jack_control__type_support.cpp.o[0m
[15.202s] [ 60%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/battery_status__type_support_c.cpp.o[0m
[15.464s] [ 61%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/can_frame__type_support.cpp.o[0m
[15.758s] [ 62%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/part_data__type_support_c.cpp.o[0m
[15.908s] [ 63%] [32m[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_cpp.so[0m
[16.023s] [ 63%] Built target sl_vcu_all__rosidl_typesupport_cpp
[16.035s] [ 64%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/battery_status__type_support.cpp.o[0m
[16.059s] [ 64%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/channel_data__type_support_c.cpp.o[0m
[16.087s] [ 65%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/srv/detail/add_can_filter__type_support.cpp.o[0m
[16.346s] [ 65%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/part_data__type_support.cpp.o[0m
[16.609s] [ 65%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/srv/detail/check_node_status__type_support.cpp.o[0m
[16.624s] [ 66%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/msg/detail/jack_status__type_support_c.cpp.o[0m
[16.631s] [ 67%] [32mBuilding CXX object CMakeFiles/can_frame_dispatcher_node.dir/src/can_frame_dispatcher_node.cpp.o[0m
[16.927s] [ 68%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/channel_data__type_support.cpp.o[0m
[17.125s] [ 69%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/srv/detail/led_control__type_support.cpp.o[0m
[17.197s] [ 70%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/sl_vcu_all/action/detail/jack_control__type_support_c.cpp.o[0m
[17.582s] [ 71%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/msg/detail/dds_fastrtps/jack_status__type_support.cpp.o[0m
[17.888s] [ 72%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/motor_info__type_support.cpp.o[0m
[17.948s] [ 72%] [32m[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_fastrtps_c.so[0m
[18.059s] [ 72%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_c
[18.071s] [ 72%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/motor_state__type_support.cpp.o[0m
[18.174s] [ 72%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/sl_vcu_all/action/detail/dds_fastrtps/jack_control__type_support.cpp.o[0m
[18.296s] [ 73%] [32mBuilding CXX object CMakeFiles/zl_motor_controller_node.dir/src/zl_motor_controller_node.cpp.o[0m
[18.452s] [ 74%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/bumper_state__type_support.cpp.o[0m
[18.851s] [ 74%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/can_frame__type_support.cpp.o[0m
[19.248s] [ 75%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/battery_status__type_support.cpp.o[0m
[19.416s] [ 76%] [32m[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so[0m
[19.544s] [ 76%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_cpp
[19.555s] [ 77%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/part_data__type_support.cpp.o[0m
[19.654s] [ 78%] [32mBuilding CXX object CMakeFiles/zl_motor_modbus_controller_node.dir/src/zl_motor_modbus_controller_node.cpp.o[0m
[19.935s] [ 78%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/channel_data__type_support.cpp.o[0m
[20.397s] [ 79%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/msg/detail/jack_status__type_support.cpp.o[0m
[20.785s] [ 79%] [32mBuilding CXX object CMakeFiles/sl_vcu_all__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/sl_vcu_all/action/detail/jack_control__type_support.cpp.o[0m
[21.701s] [ 80%] [32m[1mLinking CXX shared library libsl_vcu_all__rosidl_typesupport_introspection_cpp.so[0m
[21.825s] [ 80%] Built target sl_vcu_all__rosidl_typesupport_introspection_cpp
[21.853s] [ 80%] [32mBuilding CXX object CMakeFiles/bumper_sensor_node.dir/src/bumper_sensor_node.cpp.o[0m
[27.803s] [ 80%] [32m[1mLinking CXX executable can_frame_dispatcher_node[0m
[28.261s] [ 80%] Built target can_frame_dispatcher_node
[28.289s] [ 81%] [32mBuilding CXX object CMakeFiles/imu_sensor_node.dir/src/imu_sensor_node.cpp.o[0m
[32.194s] [ 82%] [32m[1mLinking CXX executable bumper_sensor_node[0m
[32.616s] [ 82%] Built target bumper_sensor_node
[32.645s] [ 82%] [32mBuilding CXX object CMakeFiles/teleop_key.dir/src/teleop_key.cpp.o[0m
[38.068s] [ 83%] [32m[1mLinking CXX executable teleop_key[0m
[38.429s] [ 83%] Built target teleop_key
[38.458s] [ 83%] [32mBuilding CXX object CMakeFiles/battery_monitor_node.dir/src/battery_monitor_node.cpp.o[0m
[40.142s] [ 83%] [32m[1mLinking CXX executable imu_sensor_node[0m
[40.679s] [ 83%] Built target imu_sensor_node
[40.706s] [ 84%] [32mBuilding CXX object CMakeFiles/jack_control_node.dir/src/jack_control_node.cpp.o[0m
[42.326s] [ 85%] [32m[1mLinking CXX executable zl_motor_modbus_controller_node[0m
[43.367s] [ 85%] Built target zl_motor_modbus_controller_node
[43.396s] [ 85%] [32mBuilding CXX object CMakeFiles/led_display_control_node.dir/src/led_display_control_node.cpp.o[0m
[43.749s] In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/src/jack_control_node.cpp:1[m[K:
[43.750s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include/sl_vcu_all/jack_control_node.hpp:[m[K In constructor ‘[01m[Ksl_vcu_all::JackControlNode::[01;32m[KJackControlNode[m[K(const rclcpp::NodeOptions&)[m[K’:
[43.751s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include/sl_vcu_all/jack_control_node.hpp:263:10:[m[K [01;35m[Kwarning: [m[K‘[01m[Ksl_vcu_all::JackControlNode::response_received_[m[K’ will be initialized after [[01;35m[K-Wreorder[m[K]
[43.751s]   263 |     bool [01;35m[Kresponse_received_[m[K;
[43.751s]       |          [01;35m[K^~~~~~~~~~~~~~~~~~[m[K
[43.751s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include/sl_vcu_all/jack_control_node.hpp:226:43:[m[K [01;35m[Kwarning: [m[K  ‘[01m[Kstd::chrono::_V2::steady_clock::time_point sl_vcu_all::JackControlNode::last_send_time_[m[K’ [[01;35m[K-Wreorder[m[K]
[43.751s]   226 |     std::chrono::steady_clock::time_point [01;35m[Klast_send_time_[m[K;
[43.751s]       |                                           [01;35m[K^~~~~~~~~~~~~~~[m[K
[43.751s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/src/jack_control_node.cpp:8:1:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K-Wreorder[m[K]
[43.751s]     8 | [01;35m[KJackControlNode[m[K::JackControlNode(const rclcpp::NodeOptions & options)
[43.751s]       | [01;35m[K^~~~~~~~~~~~~~~[m[K
[45.409s] [ 86%] [32m[1mLinking CXX executable battery_monitor_node[0m
[45.697s] [ 86%] Built target battery_monitor_node
[45.724s] [ 86%] Built target sl_vcu_all
[45.753s] [ 86%] [34m[1mGenerating Python code for ROS interfaces[0m
[48.096s] [ 86%] [32m[1mLinking CXX executable zl_motor_controller_node[0m
[48.415s] [ 87%] [32m[1mLinking CXX executable jack_control_node[0m
[48.762s] [ 87%] Built target jack_control_node
[49.095s] [ 88%] [32m[1mLinking CXX executable led_display_control_node[0m
[49.318s] [ 88%] Built target led_display_control_node
[49.453s] [ 88%] Built target sl_vcu_all__py
[49.481s] [ 89%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/srv/_add_can_filter_s.c.o[0m
[49.482s] [ 89%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/srv/_led_control_s.c.o[0m
[49.482s] [ 89%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/srv/_check_node_status_s.c.o[0m
[49.507s] [ 89%] Built target zl_motor_controller_node
[49.518s] [ 90%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_motor_info_s.c.o[0m
[49.690s] [ 91%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_motor_state_s.c.o[0m
[49.694s] [ 91%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_bumper_state_s.c.o[0m
[49.697s] [ 92%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_can_frame_s.c.o[0m
[49.732s] [ 93%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_battery_status_s.c.o[0m
[49.853s] [ 93%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_part_data_s.c.o[0m
[49.855s] [ 94%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_channel_data_s.c.o[0m
[49.866s] [ 94%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/msg/_jack_status_s.c.o[0m
[49.893s] [ 95%] [32mBuilding C object CMakeFiles/sl_vcu_all__rosidl_generator_py.dir/rosidl_generator_py/sl_vcu_all/action/_jack_control_s.c.o[0m
[50.151s] [ 96%] [32m[1mLinking C shared library libsl_vcu_all__rosidl_generator_py.so[0m
[50.209s] [ 96%] Built target sl_vcu_all__rosidl_generator_py
[50.236s] [ 98%] [32mBuilding C object CMakeFiles/sl_vcu_all_s__rosidl_typesupport_introspection_c.dir/rosidl_generator_py/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[50.236s] [ 98%] [32mBuilding C object CMakeFiles/sl_vcu_all_s__rosidl_typesupport_c.dir/rosidl_generator_py/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_c.c.o[0m
[50.236s] [ 98%] [32mBuilding C object CMakeFiles/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.dir/rosidl_generator_py/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[50.459s] [ 98%] [32m[1mLinking C shared module rosidl_generator_py/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so[0m
[50.461s] [ 99%] [32m[1mLinking C shared module rosidl_generator_py/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so[0m
[50.468s] [100%] [32m[1mLinking C shared module rosidl_generator_py/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so[0m
[50.504s] [100%] Built target sl_vcu_all_s__rosidl_typesupport_c
[50.505s] [100%] Built target sl_vcu_all_s__rosidl_typesupport_introspection_c
[50.514s] [100%] Built target sl_vcu_all_s__rosidl_typesupport_fastrtps_c
[50.530s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[50.542s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[50.552s] -- Install configuration: ""
[50.552s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/rosidl_interfaces/sl_vcu_all
[50.553s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.json
[50.553s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.json
[50.553s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.json
[50.553s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.json
[50.554s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.json
[50.554s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.json
[50.554s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.json
[50.554s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.json
[50.554s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.json
[50.555s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.json
[50.555s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.json
[50.555s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.json
[50.555s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all
[50.556s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action
[50.556s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail
[50.556s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.h
[50.556s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.h
[50.556s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.h
[50.556s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.c
[50.557s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c
[50.557s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__description.c
[50.557s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.h
[50.557s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv
[50.557s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.h
[50.557s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail
[50.558s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.h
[50.558s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.h
[50.558s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.c
[50.558s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.h
[50.558s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c
[50.558s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c
[50.559s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.h
[50.559s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__description.c
[50.559s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.h
[50.559s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.c
[50.559s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c
[50.559s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.c
[50.559s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__description.c
[50.560s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__description.c
[50.560s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.h
[50.560s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.h
[50.560s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.h
[50.560s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.h
[50.561s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.h
[50.561s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.h
[50.561s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg
[50.561s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.h
[50.561s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.h
[50.561s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.h
[50.561s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.h
[50.562s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_c__visibility_control.h
[50.562s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.h
[50.562s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail
[50.562s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.h
[50.562s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__description.c
[50.562s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c
[50.563s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__description.c
[50.563s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.c
[50.563s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.c
[50.563s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.h
[50.563s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c
[50.563s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.h
[50.563s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c
[50.564s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.h
[50.564s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.h
[50.564s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.h
[50.564s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.c
[50.564s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.h
[50.564s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c
[50.565s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c
[50.565s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.h
[50.565s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c
[50.565s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__description.c
[50.565s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__description.c
[50.565s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.h
[50.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.h
[50.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.h
[50.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.c
[50.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.h
[50.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.h
[50.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.h
[50.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.h
[50.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c
[50.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c
[50.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__description.c
[50.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.h
[50.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.c
[50.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.h
[50.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.c
[50.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__description.c
[50.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.h
[50.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__description.c
[50.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.c
[50.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__description.c
[50.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.h
[50.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.h
[50.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.h
[50.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.h
[50.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.c
[50.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.h
[50.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.h
[50.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.h
[50.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.h
[50.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.h
[50.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.sh
[50.571s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.dsv
[50.571s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so
[50.572s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so" to ""
[50.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all
[50.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action
[50.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail
[50.572s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_c.h
[50.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv
[50.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail
[50.572s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_c.h
[50.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_c.h
[50.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_c.h
[50.573s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg
[50.573s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail
[50.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_c.h
[50.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_c.h
[50.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_c.h
[50.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_c.h
[50.574s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_c.h
[50.574s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_c.h
[50.574s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_c.h
[50.574s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_c.h
[50.574s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[50.574s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so
[50.575s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so" to ""
[50.575s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all
[50.575s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action
[50.575s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail
[50.575s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__builder.hpp
[50.575s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__traits.hpp
[50.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.hpp
[50.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.hpp
[50.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.hpp
[50.576s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv
[50.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.hpp
[50.576s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail
[50.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.hpp
[50.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__builder.hpp
[50.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__builder.hpp
[50.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.hpp
[50.577s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__builder.hpp
[50.577s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.hpp
[50.577s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__traits.hpp
[50.577s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__traits.hpp
[50.577s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.hpp
[50.577s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__traits.hpp
[50.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.hpp
[50.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.hpp
[50.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.hpp
[50.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.hpp
[50.578s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg
[50.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.hpp
[50.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.hpp
[50.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.hpp
[50.579s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.hpp
[50.579s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail
[50.579s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__traits.hpp
[50.579s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.hpp
[50.579s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__builder.hpp
[50.579s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__traits.hpp
[50.579s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__traits.hpp
[50.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.hpp
[50.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__builder.hpp
[50.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__traits.hpp
[50.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__builder.hpp
[50.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.hpp
[50.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__traits.hpp
[50.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__traits.hpp
[50.581s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__builder.hpp
[50.581s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__builder.hpp
[50.581s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__traits.hpp
[50.581s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.hpp
[50.581s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.hpp
[50.581s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.hpp
[50.581s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.hpp
[50.582s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.hpp
[50.582s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.hpp
[50.582s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__builder.hpp
[50.582s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.hpp
[50.582s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__traits.hpp
[50.582s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.hpp
[50.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.hpp
[50.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.hpp
[50.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.hpp
[50.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.hpp
[50.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__builder.hpp
[50.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__builder.hpp
[50.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.hpp
[50.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.hpp
[50.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.hpp
[50.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.hpp
[50.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_cpp__visibility_control.hpp
[50.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.hpp
[50.585s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all
[50.585s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action
[50.585s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail
[50.585s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/dds_fastrtps
[50.585s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_cpp.hpp
[50.585s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv
[50.585s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail
[50.585s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/dds_fastrtps
[50.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_cpp.hpp
[50.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_cpp.hpp
[50.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_cpp.hpp
[50.586s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg
[50.586s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail
[50.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/dds_fastrtps
[50.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_cpp.hpp
[50.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_cpp.hpp
[50.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_cpp.hpp
[50.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_cpp.hpp
[50.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_cpp.hpp
[50.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_cpp.hpp
[50.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_cpp.hpp
[50.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_cpp.hpp
[50.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[50.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so
[50.588s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so" to ""
[50.589s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all
[50.589s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action
[50.589s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail
[50.589s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_c.h
[50.589s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c
[50.589s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv
[50.589s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail
[50.589s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_c.h
[50.589s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c
[50.590s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c
[50.590s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_c.h
[50.590s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c
[50.590s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_c.h
[50.590s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg
[50.590s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_introspection_c__visibility_control.h
[50.590s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail
[50.590s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c
[50.591s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_c.h
[50.591s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_c.h
[50.591s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c
[50.591s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c
[50.591s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_c.h
[50.591s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_c.h
[50.592s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c
[50.592s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c
[50.592s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c
[50.592s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c
[50.592s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c
[50.592s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_c.h
[50.592s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_c.h
[50.593s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_c.h
[50.593s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_c.h
[50.593s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so
[50.594s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so" to ""
[50.594s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so
[50.594s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so" to ""
[50.595s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all
[50.595s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action
[50.595s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail
[50.595s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_cpp.hpp
[50.595s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.cpp
[50.595s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv
[50.595s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail
[50.595s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.cpp
[50.595s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.cpp
[50.596s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_cpp.hpp
[50.596s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.cpp
[50.596s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_cpp.hpp
[50.596s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_cpp.hpp
[50.596s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg
[50.597s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail
[50.597s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.cpp
[50.597s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_cpp.hpp
[50.597s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.cpp
[50.597s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.cpp
[50.597s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.cpp
[50.597s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_cpp.hpp
[50.597s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_cpp.hpp
[50.598s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_cpp.hpp
[50.598s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_cpp.hpp
[50.598s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_cpp.hpp
[50.598s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.cpp
[50.598s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.cpp
[50.598s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_cpp.hpp
[50.598s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.cpp
[50.599s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_cpp.hpp
[50.599s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.cpp
[50.599s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so
[50.600s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so" to ""
[50.600s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so
[50.601s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so" to ""
[50.601s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.sh
[50.602s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.dsv
[50.602s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info
[50.602s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/SOURCES.txt
[50.602s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/PKG-INFO
[50.603s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/top_level.txt
[50.603s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/dependency_links.txt
[50.603s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all
[50.603s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_introspection_c.c
[50.603s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so
[50.603s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_c.c
[50.604s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action
[50.604s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/__init__.py
[50.604s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control.py
[50.604s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control_s.c
[50.604s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c
[50.605s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/__init__.py
[50.605s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv
[50.605s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status_s.c
[50.605s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/__init__.py
[50.605s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control.py
[50.605s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control_s.c
[50.605s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status.py
[50.606s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter.py
[50.606s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter_s.c
[50.606s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so
[50.606s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg
[50.606s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info.py
[50.606s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame_s.c
[50.607s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data_s.c
[50.607s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info_s.c
[50.607s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data.py
[50.607s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status_s.c
[50.607s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status.py
[50.607s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state_s.c
[50.607s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/__init__.py
[50.608s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status_s.c
[50.608s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state.py
[50.608s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status.py
[50.608s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame.py
[50.608s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state.py
[50.608s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data.py
[50.608s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state_s.c
[50.609s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data_s.c
[50.609s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so
[50.689s] Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all'...
[50.689s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/__init__.py'...
[50.689s] Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action'...
[50.689s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/__init__.py'...
[50.689s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control.py'...
[50.689s] Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg'...
[50.689s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/__init__.py'...
[50.689s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status.py'...
[50.689s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state.py'...
[50.689s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame.py'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data.py'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status.py'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info.py'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state.py'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data.py'...
[50.690s] Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/__init__.py'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter.py'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status.py'...
[50.690s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control.py'...
[50.696s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so
[50.696s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so" to ""
[50.697s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so
[50.697s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so" to ""
[50.697s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so
[50.698s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so" to ""
[50.698s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so
[50.699s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so" to ""
[50.699s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.idl
[50.699s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.idl
[50.700s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.idl
[50.700s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.idl
[50.700s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.idl
[50.700s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.idl
[50.700s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.idl
[50.701s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.idl
[50.701s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.idl
[50.701s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.idl
[50.701s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.idl
[50.701s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.idl
[50.702s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.srv
[50.702s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.srv
[50.703s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.srv
[50.703s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.msg
[50.704s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.msg
[50.704s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.msg
[50.705s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.msg
[50.705s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.msg
[50.705s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.msg
[50.706s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.msg
[50.706s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.msg
[50.707s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.action
[50.707s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/can_frame_dispatcher_node
[50.712s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/can_frame_dispatcher_node" to ""
[50.713s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_controller_node
[50.728s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_controller_node" to ""
[50.728s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_modbus_controller_node
[50.739s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_modbus_controller_node" to ""
[50.740s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/bumper_sensor_node
[50.744s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/bumper_sensor_node" to ""
[50.744s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/imu_sensor_node
[50.749s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/imu_sensor_node" to ""
[50.749s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/teleop_key
[50.750s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/teleop_key" to ""
[50.750s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/battery_monitor_node
[50.752s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/battery_monitor_node" to ""
[50.753s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/jack_control_node
[50.755s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/jack_control_node" to ""
[50.755s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/led_display_control_node
[50.756s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/led_display_control_node" to ""
[50.757s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch
[50.757s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/battery_monitor.launch.py
[50.757s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/jack_control.launch.py
[50.758s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/robot_localization_ekf.launch.py
[50.758s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_controller.launch.py
[50.758s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_modbus_controller.launch.py
[50.759s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/led_control.launch.py
[50.759s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/vcu_nodes.launch.py
[50.759s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/imu_sensor.launch.py
[50.760s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/bumper_sensor.launch.py
[50.760s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/sl_vcu_all.launch.py
[50.760s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config
[50.761s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/ekf.yaml
[50.761s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_modbus_controller.yaml
[50.761s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/battery_monitor.yaml
[50.762s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_controller.yaml
[50.762s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/jack_control.yaml
[50.762s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/imu_sensor.yaml
[50.763s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/robot_localization_ekf.yaml
[50.763s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/bumper_sensor.yaml
[50.763s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/led_control.yaml
[50.764s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/package_run_dependencies/sl_vcu_all
[50.764s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/parent_prefix_path/sl_vcu_all
[50.764s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.sh
[50.765s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.dsv
[50.765s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.sh
[50.766s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.dsv
[50.766s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.bash
[50.766s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.sh
[50.766s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.zsh
[50.767s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.dsv
[50.767s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv
[50.767s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/packages/sl_vcu_all
[50.767s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport.cmake
[50.768s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport-noconfig.cmake
[50.768s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport.cmake
[50.768s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[50.768s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cppExport.cmake
[50.769s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport.cmake
[50.769s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[50.769s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport.cmake
[50.769s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport-noconfig.cmake
[50.770s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport.cmake
[50.770s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport-noconfig.cmake
[50.770s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport.cmake
[50.770s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[50.771s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport.cmake
[50.771s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport-noconfig.cmake
[50.771s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport.cmake
[50.771s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport-noconfig.cmake
[50.771s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake-extras.cmake
[50.772s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_dependencies-extras.cmake
[50.772s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_include_directories-extras.cmake
[50.772s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_libraries-extras.cmake
[50.772s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_targets-extras.cmake
[50.773s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[50.773s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[50.773s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig.cmake
[50.773s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig-version.cmake
[50.773s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.xml
[50.776s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
