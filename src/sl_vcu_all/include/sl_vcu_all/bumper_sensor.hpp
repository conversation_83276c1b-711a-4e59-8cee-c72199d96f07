#ifndef SL_VCU_ALL_BUMPER_SENSOR_HPP
#define SL_VCU_ALL_BUMPER_SENSOR_HPP

#include "rclcpp/rclcpp.hpp"
#include "sl_vcu_all/msg/bumper_state.hpp"
#include "std_msgs/msg/bool.hpp"
#include <chrono>
#include <functional>
#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <fstream>
#include <fcntl.h>
#include <unistd.h>

namespace sl_vcu_all
{

class BumperSensor : public rclcpp::Node
{
public:
    explicit BumperSensor(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    virtual ~BumperSensor();

private:
    // Methods
    void initParameters();
    bool initGpioFiles();
    void monitorGpioValues();
    void publishBumperState();
    bool readGpioValue(int gpio_fd);
    void disableCallback(const std_msgs::msg::Bool::SharedPtr msg);

    // Publisher
    rclcpp::Publisher<sl_vcu_all::msg::BumperState>::SharedPtr bumper_state_pub_;

    // Subscriber
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr disable_sub_;

    // GPIO parameters
    std::string front_bumper_gpio_path_;
    std::string back_bumper_gpio_path_;
    int front_bumper_trigger_value_;
    int back_bumper_trigger_value_;
    int gpio_check_period_ms_;
    int gpio_debounce_time_ms_;

    // GPIO file descriptors
    int front_bumper_gpio_fd_;
    int back_bumper_gpio_fd_;

    // Bumper state
    std::atomic<bool> front_bumper_triggered_;
    std::atomic<bool> back_bumper_triggered_;
    std::atomic<uint32_t> bumper_status_;

    // GPIO monitoring thread
    std::thread gpio_thread_;
    std::atomic<bool> keep_monitoring_;

    // Debounce state tracking
    std::chrono::steady_clock::time_point front_last_change_time_;
    std::chrono::steady_clock::time_point back_last_change_time_;
    bool front_last_stable_state_;
    bool back_last_stable_state_;

    // Timer for publishing
    rclcpp::TimerBase::SharedPtr publish_timer_;
    int publish_rate_ms_;
    std::string bumper_topic_;

    // Disable functionality
    std::atomic<bool> bumper_disabled_;
    std::chrono::steady_clock::time_point last_disable_update_time_;
    int disable_timeout_ms_;
};

} // namespace sl_vcu_all

#endif // SL_VCU_ALL_BUMPER_SENSOR_HPP 