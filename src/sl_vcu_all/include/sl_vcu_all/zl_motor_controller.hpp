#ifndef ZL_MOTOR_CONTROLLER_HPP_
#define ZL_MOTOR_CONTROLLER_HPP_

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/joint_state.hpp>
#include <sl_vcu_all/msg/can_frame.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2/LinearMath/Quaternion.h>
#include <string>
#include <unordered_map>
#include <vector>
#include <mutex>
#include <cmath>
#include <thread>
#include <chrono>
#include <queue>
#include <condition_variable>
#include <deque>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <net/if.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <unistd.h>

// Include the generated service headers
#include "sl_vcu_all/srv/check_node_status.hpp"
#include "sl_vcu_all/srv/add_can_filter.hpp"
#include "sl_vcu_all/msg/motor_info.hpp"
#include "sl_vcu_all/msg/motor_state.hpp"
#include "sl_vcu_all/msg/bumper_state.hpp"
#include <std_msgs/msg/bool.hpp>
#include "sensor_msgs/msg/imu.hpp"

// Forward declarations of service types
namespace sl_vcu_all {
namespace srv {
class CheckNodeStatus;
class AddCanFilter;
}  // namespace srv
}  // namespace sl_vcu_all

// CANopen SDO command codes
#define SDO_READ_REQUEST                  0x40
#define SDO_READ_RESPONSE_1BYTE           0x4F
#define SDO_READ_RESPONSE_2BYTE           0x4B
#define SDO_READ_RESPONSE_4BYTE           0x43
#define SDO_READ_RESPONSE_ERROR           0x80

#define SDO_WRITE_REQUEST_1BYTE           0x2F
#define SDO_WRITE_REQUEST_2BYTE           0x2B
#define SDO_WRITE_REQUEST_4BYTE           0x23

#define SDO_WRITE_RESPONSE_OK             0x60
#define SDO_WRITE_RESPONSE_ERROR          0x80

// Helper macro to check if a command is a successful read response
#define IS_SDO_READ_RESPONSE_OK(cmd) ((cmd) == SDO_READ_RESPONSE_1BYTE || (cmd) == SDO_READ_RESPONSE_2BYTE || (cmd) == SDO_READ_RESPONSE_4BYTE)

// ZL Motor SDO indices
#define ZL_INDEX_CTRL                     0x6040
// ZL Motor control commands
#define ZL_CTRL_RELEASE                   0x06
#define ZL_CTRL_ENABLE                    0x07
#define ZL_CTRL_SPEED_ENABLE              0x0F
#define ZL_CTRL_ALARM_CLEAR               0x80

// ZL Motor run modes
#define ZL_INDEX_RUN_MODE                 0x6060
// ZL Motor run modes
#define ZL_RUN_MODE_POSITION              0x01
#define ZL_RUN_MODE_SPEED                 0x03
#define ZL_RUN_MODE_TORQUE                0x04

#define ZL_INDEX_DEST_SPEED               0x60FF
#define ZL_INDEX_POS                      0x6064
#define ZL_INDEX_SPEED                    0x606C
#define ZL_INDEX_ACC_SPEED_TIME           0x6083
#define ZL_INDEX_DEC_SPEED_TIME           0x6084

// ZL Motor SDO subindices
#define ZL_SUBINDEX_DEFAULT               0x00
#define ZL_SUBINDEX_LEFT                  0x01
#define ZL_SUBINDEX_RIGHT                 0x02
#define ZL_SUBINDEX_L_R                   0x03

#define ZL_INDEX_CURRENT_FB               0x6077
#define ZL_SUBINDEX_CURRENT_FB_LEFT       0x01
#define ZL_SUBINDEX_CURRENT_FB_RIGHT      0x02

#define ZL_INDEX_TEMPERATURE              0x2032
#define ZL_SUBINDEX_TEMPERATURE_LEFT      0x01
#define ZL_SUBINDEX_TEMPERATURE_RIGHT     0x02
#define ZL_SUBINDEX_TEMPERATURE_DRIVER    0x03

#define ZL_INDEX_LAST_ALARM               0x603F
#define ZL_SUBINDEX_LAST_ALARM            0x00

// GPIO input bit definitions
#define ZL_INDEX_DIN_STATUS               0x2003
#define GPIO_I_EMERGENCY_BIT0               0x01  // Emergency signal bit 0
#define GPIO_I_EMERGENCY_BIT1               0x02  // Emergency signal bit 1
#define GPIO_I_BRAKE_RELEASE                0x04  // Brake release signal bit 2
#define GPIO_I_POWER_OFF_BUTTON             0x08  // Power off button signal bit 3

// GPIO output bit definitions
#define ZL_INDEX_DOUT_CONTROL             0x2030
#define ZL_SUBINDEX_DOUT_CONTROL          0x04
#define GPIO_O_POWER_OFF_BUTTON_LIGHT     0x0040  // Power off button light signal bit 6
#define GPIO_O_POWER_CONTROL              0x0100  // Power control signal bit 8


namespace sl_vcu_all
{

class ZLMotorController : public rclcpp::Node
{
public:
    explicit ZLMotorController(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    virtual ~ZLMotorController();

    struct SdoRequest;
    using SdoCallback = std::function<void(bool success, std::shared_ptr<SdoRequest> request, uint32_t data)>;

    // SDO response tracking
    struct SdoRequest {
      uint16_t index;
      uint8_t subindex;
      uint8_t command;
      uint32_t data;
      rclcpp::Time timestamp;
      bool awaiting_response;
      SdoCallback callback;
    };


  private:
    // CAN communication
    uint32_t can_id_tx_;
    uint32_t can_id_rx_;

    // Socket CAN related
    int socket_fd_;
    std::string can_interface_;
    bool is_socket_initialized_;
    bool use_sockcan_direct_;

    // Socket CAN receive thread
    std::thread receive_thread_;
    bool running_;

    // Socket CAN send thread
    std::thread send_thread_;
    std::queue<sl_vcu_all::msg::CanFrame> send_queue_;
    std::mutex send_queue_mutex_;
    std::condition_variable send_queue_cv_;
    std::chrono::steady_clock::time_point last_send_time_;
    int min_send_interval_ms_;

    // Robot parameters
    // double wheel_diameter_;
    // double wheel_radius_;

    double wheel_diameter_left_;
    double wheel_diameter_right_;
    double wheel_radius_left_;
    double wheel_radius_right_;

    double wheel_separation_;
    double gear_ratio_;
    double encoder_resolution_;

    // Motor state
    bool motors_enabled_;
    int32_t left_pos_;
    int32_t right_pos_;
    int32_t left_pos_prev_;
    int32_t right_pos_prev_;
    bool is_last_left_pos_init_;
    bool is_last_right_pos_init_;
    int32_t left_speed_;
    int32_t right_speed_;
    int16_t left_current_;
    int16_t right_current_;
    uint32_t gpio_status_;
    int16_t left_temp_;
    int16_t right_temp_;
    int16_t driver_temp_;
    uint32_t last_alarm_;
    // Odometry
    double x_;
    double y_;
    double theta_;
    // Odometry raw
    double positions_raw_[2];
    rclcpp::Time last_odom_time_;
    std::string odom_frame_id_;
    std::string base_frame_id_;
    bool publish_tf_;
    bool print_status_;

    // Topic names
    std::string cmd_vel_topic_;
    std::string odom_topic_;
    std::string filtered_odom_topic_;
    std::string joint_state_topic_;
    std::string can_tx_topic_;
    std::string can_rx_topic_;
    std::string motor_info_topic_;
    std::string motor_state_topic_;
    std::string bumper_topic_;

    // Service names
    std::string check_status_service_;
    std::string add_filter_service_;

    // Control parameters
    int control_cycle_ms_;
    int cycle_counter_;
    bool check_dispatcher_node_;
    bool dispatcher_ready_;
    int emergency_stop_bit0_trigger_level_;  // 0 = low level triggers emergency stop, 1 = high level triggers emergency stop



    std::vector<std::shared_ptr<SdoRequest>> pending_sdo_requests_;
    std::mutex sdo_mutex_;

    // Mutex for thread safety
    std::mutex motor_mutex_;

    // ROS publishers and subscribers
    rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::CanFrame>::SharedPtr can_tx_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::MotorInfo>::SharedPtr motor_info_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::MotorState>::SharedPtr motor_state_pub_;
    rclcpp::Publisher<std_msgs::msg::Bool>::SharedPtr power_off_pub_;
    rclcpp::Publisher<sensor_msgs::msg::JointState>::SharedPtr joint_state_pub_;
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_sub_;
    rclcpp::Subscription<sl_vcu_all::msg::CanFrame>::SharedPtr can_rx_sub_;
    rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr bumper_sub_;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr filtered_odom_sub_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr alarm_clear_sub_;
    rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr filtered_imu_sub_;

    // ROS clients - use specific service types
    rclcpp::Client<srv::CheckNodeStatus>::SharedPtr check_status_client_;
    rclcpp::Client<srv::AddCanFilter>::SharedPtr add_filter_client_;

    // TF broadcaster
    std::unique_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;

    // Timers
    rclcpp::TimerBase::SharedPtr control_timer_;
    rclcpp::TimerBase::SharedPtr status_timer_;

    // Motor control state machine
    enum class ControlState {
      INIT,
      CHECK_DISPATCHER,
      REGISTER_FILTER,
      SET_MODE,
      ENABLE_MOTORS_STEP_1,
      ENABLE_MOTORS_STEP_2,
      ENABLE_MOTORS_STEP_3,
      CLEAR_ALARM,

      RUNNING,
      ERROR
    };
    ControlState control_state_;
    int state_counter_;


    rclcpp::Time last_cmd_vel_time_;
    rclcpp::Time last_bumper_time_;

    int cmd_vel_timeout_ms_;
    int bumper_timeout_ms_;
    int status_update_cycle_ms_;
    int sdo_response_timeout_ms_;
    bool publish_motor_info_;
    geometry_msgs::msg::Twist latest_cmd_vel_;
    sl_vcu_all::msg::BumperState latest_bumper_state_;
    
    // Power off control parameters
    bool power_off_flag_;
    int power_off_timeout_ms_;
    std::string power_off_topic_;
    int power_off_publish_period_ms_;
    rclcpp::Time power_off_start_time_;

    // IMU time offset and buffering
    int imu_time_offset_ms_;
    
    struct ImuYawSample {
        rclcpp::Time timestamp;
        double yaw;
    };
    
    std::deque<ImuYawSample> imu_yaw_buffer_;
    std::mutex imu_buffer_mutex_;

private:
    // Methods
    void initParameters();
    void cmdVelCallback(const geometry_msgs::msg::Twist::SharedPtr msg);
    void canRxCallback(const sl_vcu_all::msg::CanFrame::SharedPtr msg);
    void bumperCallback(const sl_vcu_all::msg::BumperState::SharedPtr msg);
    void filteredOdomCallback(const nav_msgs::msg::Odometry::SharedPtr msg);
    void filteredImuCallback(const sensor_msgs::msg::Imu::SharedPtr msg);
    void clearAlarmCallback(const std_msgs::msg::Bool::SharedPtr msg);
    void controlTimerCallback();
    void statusTimerCallback();
    void setRealTimeProcessPriority();

    // CANopen SDO protocol methods
    bool sendSDO(uint16_t index, uint8_t subindex, uint32_t data, uint8_t command,
      SdoCallback callback = nullptr);
    void processSDOResponse(const sl_vcu_all::msg::CanFrame::SharedPtr msg);
    void checkSdoTimeouts();

    // Motor control methods
    void setSpeedMode();
    void enableMotors_step_1();
    void enableMotors_step_2();
    void enableMotors_step_3();


    //void enableMotors();
    void disableMotors();

    void setMotorSpeeds(double left_speed_rpm, double right_speed_rpm);
    void emergencyStop();

    // Status update methods
    void updateMotorPositions();
    void updateMotorSpeeds();
    void updateMotorCurrents();
    void updateMotorTemperatures();
    void updateGpioStatus();
    void updateLastAlarm();

    // Power off control methods
    void updatePowerOffControl();
    void turnOffPowerButtonLight();
    void turnOffAllPower();
    void publishPowerOffStatus();
    // Odometry methods
    void updateOdometry();
    void publishOdometry();

    // Motor info methods
    void publishMotorInfo();
    void publishMotorState();

    // Service client methods
    void checkDispatcherNode();
    void registerCanFilter();

    // Socket CAN methods
    bool initSocketCan();
    bool sendCanFrameDirect(const sl_vcu_all::msg::CanFrame& frame);
    bool queueCanFrameForSend(const sl_vcu_all::msg::CanFrame& frame);  // Queue frame for send thread
    void receiveCanFramesThread();  // Thread function for receiving CAN frames
    void sendCanFramesThread();     // Thread function for sending CAN frames with timing control

    // Utility methods
    double rpmToRadPerSec(double rpm);
    double radPerSecToRpm(double rad_per_sec);
    int32_t rpmToEncoderSpeed(double rpm);
    double encoderToRpm(int32_t encoder_speed);
    double encoderToRadians(int32_t encoder_ticks);
    double normalizeAngle(double angle);
    bool isEmergencyStopBit0Triggered(uint32_t gpio_status);  // Check emergency stop bit0 based on trigger level
    
    // IMU utility methods
    bool getImuYawAtTime(const rclcpp::Time& target_time, double& yaw);
};

}  // namespace sl_vcu_all

#endif  // ZL_MOTOR_CONTROLLER_HPP_
