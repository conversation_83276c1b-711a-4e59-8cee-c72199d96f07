#ifndef ZL_MOTOR_MODBUS_CONTROLLER_HPP_
#define ZL_MOTOR_MODBUS_CONTROLLER_HPP_

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2/LinearMath/Quaternion.h>
#include <string>
#include <mutex>
#include <cmath>
#include <thread>
#include <chrono>
#include <modbus.h>

// Include the generated message headers
#include "sl_vcu_all/msg/motor_info.hpp"
#include "sl_vcu_all/msg/motor_state.hpp"
#include "sl_vcu_all/msg/bumper_state.hpp"
#include <std_msgs/msg/bool.hpp>
#include "sensor_msgs/msg/imu.hpp"

// ZL Motor Modbus Register Addresses (based on PDF documentation)
// Mode register
#define ZL_MODBUS_REG_MODE                0x200D  // mode register
#define ZL_MODBUS_MODE_SPEED              0x0003  // Speed mode

// Control registers
#define ZL_MODBUS_REG_CONTROL             0x200E  // Control register
// Control commands for register 0x200E
#define ZL_MODBUS_CTRL_DISABLE            0x0007       // Disable control
#define ZL_MODBUS_CTRL_ENABLE             0x0008       // Enable control  
#define ZL_MODBUS_CTRL_CLEAR_ALARM        0x0006       // Clear alarm

#define ZL_MODBUS_REG_SPEED_SET           0x2088  // Speed setting register


// Status registers (read-only)
#define ZL_MODBUS_REG_GPIO_STATUS         0x2003  // GPIO status
#define ZL_MODBUS_REG_MOTOR_TEMPS         0x20A4  // Motor temperatures
#define ZL_MODBUS_REG_LEFT_ALARM          0x20A5  // Left motor alarm code
#define ZL_MODBUS_REG_RIGHT_ALARM         0x20A6  // Right motor alarm code
#define ZL_MODBUS_REG_LEFT_POS            0x20A7  // Left motor position (2 registers)
#define ZL_MODBUS_REG_RIGHT_POS           0x20A9  // Right motor position (2 registers)
#define ZL_MODBUS_REG_LEFT_SPEED_FB       0x20AB  // Left motor speed feedback
#define ZL_MODBUS_REG_RIGHT_SPEED_FB      0x20AC  // Right motor speed feedback
#define ZL_MODBUS_REG_LEFT_CURRENT        0x20AD  // Left motor current
#define ZL_MODBUS_REG_RIGHT_CURRENT       0x20AE  // Right motor current
#define ZL_MODBUS_REG_DRIVER_TEMP         0x20B0  // Driver temperature

// GPIO bit definitions (same as CAN version)
#define GPIO_EMERGENCY_BIT0               0x01  // Emergency signal bit 0
#define GPIO_EMERGENCY_BIT1               0x02  // Emergency signal bit 1
#define GPIO_BRAKE_RELEASE                0x04  // Brake release signal bit 2
#define GPIO_POWER_OFF_BUTTON             0x08  // Power off button signal bit 3

namespace sl_vcu_all
{

class ZLMotorModbusController : public rclcpp::Node
{
public:
    explicit ZLMotorModbusController(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    virtual ~ZLMotorModbusController();

private:
    // Modbus communication
    modbus_t* modbus_ctx_;
    std::string serial_device_;
    int baud_rate_;
    char parity_;
    int data_bits_;
    int stop_bits_;
    int slave_address_;
    bool modbus_connected_;
    std::mutex modbus_mutex_;

    // Robot parameters
    double wheel_diameter_left_;
    double wheel_diameter_right_;
    double wheel_radius_left_;
    double wheel_radius_right_;
    double wheel_separation_;
    double gear_ratio_;
    double encoder_resolution_;

    // Motor state
    bool motors_enabled_;
    int32_t left_pos_;
    int32_t right_pos_;
    int32_t left_pos_prev_;
    int32_t right_pos_prev_;
    bool is_last_left_pos_init_;
    bool is_last_right_pos_init_;
    int16_t left_speed_;
    int16_t right_speed_;
    int16_t left_current_;
    int16_t right_current_;
    uint16_t gpio_status_;
    int16_t left_temp_;
    int16_t right_temp_;
    int16_t driver_temp_;
    uint16_t left_alarm_;
    uint16_t right_alarm_;

    // Odometry
    double x_;
    double y_;
    double theta_;
    rclcpp::Time last_odom_time_;
    std::string odom_frame_id_;
    std::string base_frame_id_;
    bool publish_tf_;
    bool print_status_;

    // Topic names
    std::string cmd_vel_topic_;
    std::string odom_topic_;
    std::string motor_info_topic_;
    std::string motor_state_topic_;
    std::string bumper_topic_;

    // Control parameters
    int control_cycle_ms_;
    int cycle_counter_;
    int emergency_stop_bit0_trigger_level_;  // 0 = low level triggers emergency stop, 1 = high level triggers emergency stop

    // Mutex for thread safety
    std::mutex motor_mutex_;

    // ROS publishers and subscribers
    rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::MotorInfo>::SharedPtr motor_info_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::MotorState>::SharedPtr motor_state_pub_;
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_sub_;
    rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr bumper_sub_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr alarm_clear_sub_;
    rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr filtered_imu_sub_;

    // TF broadcaster
    std::unique_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;

    // Timers
    rclcpp::TimerBase::SharedPtr control_timer_;
    rclcpp::TimerBase::SharedPtr status_timer_;

    // Motor control state machine
    enum class ControlState {
        INIT,
        CONNECTING,
        SET_MODE,
        ENABLE_MOTORS,
        RUNNING,
        CLEAR_ALARM,
        ERROR
    };
    ControlState control_state_;
    int state_counter_;

    rclcpp::Time last_cmd_vel_time_;
    rclcpp::Time last_bumper_time_;

    int cmd_vel_timeout_ms_;
    int bumper_timeout_ms_;
    int response_timeout_ms_;
    int status_update_cycle_ms_;
    bool publish_motor_info_;
    geometry_msgs::msg::Twist latest_cmd_vel_;
    sl_vcu_all::msg::BumperState latest_bumper_state_;

private:
    // Methods
    void initParameters();
    void cmdVelCallback(const geometry_msgs::msg::Twist::SharedPtr msg);
    void bumperCallback(const sl_vcu_all::msg::BumperState::SharedPtr msg);
    void filteredImuCallback(const sensor_msgs::msg::Imu::SharedPtr msg);
    void clearAlarmCallback(const std_msgs::msg::Bool::SharedPtr msg);
    void controlTimerCallback();
    void statusTimerCallback();

    // Modbus communication methods
    bool initModbus();
    void closeModbus();
    bool writeRegister(uint16_t address, uint16_t value);
    bool writeRegisters(uint16_t address, uint16_t count, uint16_t* values);
    bool readRegisters(uint16_t address, uint16_t count, uint16_t* dest);
    bool readSingleRegister(uint16_t address, uint16_t* value);

    // Motor control methods
    void enableMotors();
    void disableMotors();
    void clearAlarm();
    void setMotorSpeeds(double left_speed_rpm, double right_speed_rpm);

    // Status update methods
    void updateMotorAllData();
    void updateMotorPositions();
    void updateMotorSpeeds();
    void updateMotorCurrents();
    void updateMotorTemperatures();
    void updateGpioStatus();
    void updateAlarmCodes();

    // Odometry methods
    void updateOdometry();
    void publishOdometry();

    // Motor info methods
    void publishMotorInfo();
    void publishMotorState();

    // Utility methods
    double rpmToRadPerSec(double rpm);
    double radPerSecToRpm(double rad_per_sec);
    int16_t rpmToModbusSpeed(double rpm);
    double modbusSpeedToRpm(int16_t modbus_speed);
    double encoderToRadians(int32_t encoder_ticks);
    double normalizeAngle(double angle);
    bool isEmergencyStopBit0Triggered(uint16_t gpio_status);  // Check emergency stop bit0 based on trigger level
};

}  // namespace sl_vcu_all

#endif  // ZL_MOTOR_MODBUS_CONTROLLER_HPP_
