# Jack Status Message
# This message contains the current status information of the jack control system

std_msgs/Header header

# Current operational stage
string current_stage        # Current stage: "init", "detecting_base", "base_stop", "lifting_up", "lifting_down", "top_stop", "middle_stop"

# Position and status information
int32 current_position      # Current position in encoder counts
uint16 current_status       # Current status word from jack controller
uint16 current_alarm        # Current alarm code from jack controller 