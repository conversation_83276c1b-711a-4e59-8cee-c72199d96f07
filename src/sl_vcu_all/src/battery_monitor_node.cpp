#include "sl_vcu_all/battery_monitor.hpp"
#include <cstring>
#include <chrono>
#include <functional>
#include <sstream>
#include <iomanip>
#include <linux/input.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <errno.h>
#include <poll.h>
#include <linux/input-event-codes.h>

using namespace std::chrono_literals;

namespace sl_vcu_all
{

BatteryMonitor::BatteryMonitor(const rclcpp::NodeOptions & options)
    : Node("battery_monitor", options),
    socket_fd_(-1),
    is_socket_initialized_(false),
    running_(false),
    last_send_time_(std::chrono::steady_clock::now()),
    min_send_interval_ms_(20),
    request_interval_ms_(1000),
    is_charging_(false),
    keep_monitoring_(true),
    is_manual_charging_(false),
    auto_charging_gpio_fd_(-1),
    manual_charging_gpio_fd_(-1),
    auto_charging_last_stable_state_(false),
    manual_charging_last_stable_state_(false),
    voltage_(0.0),
    current_(0.0),
    capacity_(0.0),
    cycle_count_(0),
    percentage_(0.0),
    publish_rate_ms_(1000),
    battery_status_publish_rate_ms_(10),
    battery_topic_("battery_state"),
    battery_status_topic_("battery_status"),
    can_id_battery_info_(0x100),
    can_id_battery_status_(0x101),
    publish_timer_(nullptr),
    battery_status_timer_(nullptr),
    request_timer_(nullptr)
{
    // Initialize parameters
    initParameters();

    RCLCPP_INFO(this->get_logger(), "Initializing Battery Monitor");

    // Create publishers
    battery_state_pub_ = this->create_publisher<sensor_msgs::msg::BatteryState>(battery_topic_, 10);
    battery_status_pub_ = this->create_publisher<sl_vcu_all::msg::BatteryStatus>(battery_status_topic_, 10);

    // Initialize socket CAN
    is_socket_initialized_ = initSocketCan();
    if (!is_socket_initialized_) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize SocketCAN");
    } else {
        RCLCPP_INFO(this->get_logger(), "SocketCAN initialized successfully on interface %s", can_interface_.c_str());

        // Start the CAN monitoring thread
        running_ = true;
        can_thread_ = std::thread(&BatteryMonitor::monitorCanFrames, this);
    }

    // Initialize GPIO files
    bool gpio_ok = initGpioFiles();
    if (!gpio_ok) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize GPIO files for charging status");
    }

    // Initialize GPIO debounce timestamps
    auto_charging_last_change_time_ = std::chrono::steady_clock::now();
    manual_charging_last_change_time_ = std::chrono::steady_clock::now();

    // Start GPIO monitoring thread
    gpio_thread_ = std::thread(&BatteryMonitor::monitorGpioValues, this);

    // Create timer for publishing battery state
    publish_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(publish_rate_ms_),
        std::bind(&BatteryMonitor::publishBatteryState, this));

    // Create timer for publishing battery status
    battery_status_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(battery_status_publish_rate_ms_),
        std::bind(&BatteryMonitor::publishBatteryStatus, this));

    // Create timer for requesting battery status
    request_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(request_interval_ms_),
        std::bind(&BatteryMonitor::requestBatteryStatus, this));

    RCLCPP_INFO(this->get_logger(), "Battery Monitor initialized");
}

BatteryMonitor::~BatteryMonitor()
{
    // Stop monitoring threads
    running_ = false;
    keep_monitoring_ = false;

    if (can_thread_.joinable()) {
        can_thread_.join();
    }

    if (gpio_thread_.joinable()) {
        gpio_thread_.join();
    }

    // Close socket if initialized
    if (socket_fd_ >= 0) {
        close(socket_fd_);
        socket_fd_ = -1;
    }

    // Close GPIO file descriptors
    if (auto_charging_gpio_fd_ >= 0) {
        close(auto_charging_gpio_fd_);
    }
    if (manual_charging_gpio_fd_ >= 0) {
        close(manual_charging_gpio_fd_);
    }
}

void BatteryMonitor::initParameters()
{
    // CAN communication parameters
    this->declare_parameter("can_interface", "can0");
    this->declare_parameter("can_id_battery_info", 0x100);
    this->declare_parameter("can_id_battery_status", 0x101);
    this->declare_parameter("min_send_interval_ms", 20);
    this->declare_parameter("request_interval_ms", 1000);

    // GPIO parameters
    this->declare_parameter("auto_charging_gpio_path", "/sys/class/gpio/gpio39/value");
    this->declare_parameter("manual_charging_gpio_path", "/sys/class/gpio/gpio27/value");
    this->declare_parameter("auto_charging_trigger_value", 0);
    this->declare_parameter("manual_charging_trigger_value", 0);
    this->declare_parameter("gpio_check_period_ms", 10);
    this->declare_parameter("gpio_debounce_time_ms", 10);

    // Topic and publishing parameters
    this->declare_parameter("battery_topic", "battery_state");
    this->declare_parameter("battery_status_topic", "battery_status");
    this->declare_parameter("publish_rate_ms", 1000);
    this->declare_parameter("battery_status_publish_rate_ms", 20);

    // Get parameter values
    can_interface_ = this->get_parameter("can_interface").as_string();
    can_id_battery_info_ = this->get_parameter("can_id_battery_info").as_int();
    can_id_battery_status_ = this->get_parameter("can_id_battery_status").as_int();
    min_send_interval_ms_ = this->get_parameter("min_send_interval_ms").as_int();
    request_interval_ms_ = this->get_parameter("request_interval_ms").as_int();
    auto_charging_gpio_path_ = this->get_parameter("auto_charging_gpio_path").as_string();
    manual_charging_gpio_path_ = this->get_parameter("manual_charging_gpio_path").as_string();
    auto_charging_trigger_value_ = this->get_parameter("auto_charging_trigger_value").as_int();
    manual_charging_trigger_value_ = this->get_parameter("manual_charging_trigger_value").as_int();
    gpio_check_period_ms_ = this->get_parameter("gpio_check_period_ms").as_int();
    gpio_debounce_time_ms_ = this->get_parameter("gpio_debounce_time_ms").as_int();
    battery_topic_ = this->get_parameter("battery_topic").as_string();
    battery_status_topic_ = this->get_parameter("battery_status_topic").as_string();
    publish_rate_ms_ = this->get_parameter("publish_rate_ms").as_int();
    battery_status_publish_rate_ms_ = this->get_parameter("battery_status_publish_rate_ms").as_int();

    RCLCPP_INFO(this->get_logger(), "Parameters initialized:");
    RCLCPP_INFO(this->get_logger(), "  CAN interface: %s", can_interface_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Battery info ID: 0x%X", can_id_battery_info_);
    RCLCPP_INFO(this->get_logger(), "  Battery status ID: 0x%X", can_id_battery_status_);
    RCLCPP_INFO(this->get_logger(), "  Min send interval: %d ms", min_send_interval_ms_);
    RCLCPP_INFO(this->get_logger(), "  Request interval: %d ms", request_interval_ms_);
    RCLCPP_INFO(this->get_logger(), "  Auto charging GPIO path: %s", auto_charging_gpio_path_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Manual charging GPIO path: %s", manual_charging_gpio_path_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Auto charging trigger value: %d", auto_charging_trigger_value_);
    RCLCPP_INFO(this->get_logger(), "  Manual charging trigger value: %d", manual_charging_trigger_value_);
    RCLCPP_INFO(this->get_logger(), "  GPIO check period: %d ms", gpio_check_period_ms_);
    RCLCPP_INFO(this->get_logger(), "  GPIO debounce time: %d ms", gpio_debounce_time_ms_);
    RCLCPP_INFO(this->get_logger(), "  Battery state publish rate: %d ms", publish_rate_ms_);
    RCLCPP_INFO(this->get_logger(), "  Battery status publish rate: %d ms", battery_status_publish_rate_ms_);
}

bool BatteryMonitor::initSocketCan()
{
    // Create socket
    socket_fd_ = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (socket_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Error creating CAN socket: %s", strerror(errno));
        return false;
    }

    // Get interface index
    struct ifreq ifr;
    std::strcpy(ifr.ifr_name, can_interface_.c_str());
    if (ioctl(socket_fd_, SIOCGIFINDEX, &ifr) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Error getting interface index for %s: %s",
                     can_interface_.c_str(), strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    // Bind socket to the CAN interface
    struct sockaddr_can addr;
    std::memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;
    if (bind(socket_fd_, reinterpret_cast<struct sockaddr*>(&addr), sizeof(addr)) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Error binding socket to interface %s: %s",
                     can_interface_.c_str(), strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    // Set up filters for battery info and status CAN IDs
    struct can_filter rfilter[2];
    rfilter[0].can_id = can_id_battery_info_;
    rfilter[0].can_mask = CAN_SFF_MASK;
    rfilter[1].can_id = can_id_battery_status_;
    rfilter[1].can_mask = CAN_SFF_MASK;

    if (setsockopt(socket_fd_, SOL_CAN_RAW, CAN_RAW_FILTER, rfilter, sizeof(rfilter)) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Error setting receive filters: %s", strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    RCLCPP_INFO(this->get_logger(), "Successfully initialized SocketCAN on interface %s", can_interface_.c_str());
    return true;
}

bool BatteryMonitor::initGpioFiles()
{
    // Open auto charging GPIO file
    auto_charging_gpio_fd_ = open(auto_charging_gpio_path_.c_str(), O_RDONLY);
    if (auto_charging_gpio_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to open auto charging GPIO file: %s", auto_charging_gpio_path_.c_str());
        return false;
    }

    // Open manual charging GPIO file
    manual_charging_gpio_fd_ = open(manual_charging_gpio_path_.c_str(), O_RDONLY);
    if (manual_charging_gpio_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to open manual charging GPIO file: %s", manual_charging_gpio_path_.c_str());
        close(auto_charging_gpio_fd_);
        auto_charging_gpio_fd_ = -1;
        return false;
    }

    RCLCPP_INFO(this->get_logger(), "Successfully opened charging GPIO files");
    return true;
}

bool BatteryMonitor::readGpioValue(int gpio_fd)
{
    if (gpio_fd < 0) {
        return false;
    }

    // Seek to beginning of file
    if (lseek(gpio_fd, 0, SEEK_SET) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to seek GPIO file");
        return false;
    }

    char buffer[8];
    ssize_t bytes_read = read(gpio_fd, buffer, sizeof(buffer) - 1);
    if (bytes_read <= 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to read GPIO value");
        return false;
    }

    buffer[bytes_read] = '\0';

    try {
        int value = std::stoi(buffer);
        return (value == 1);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Failed to parse GPIO value '%s': %s", buffer, e.what());
        return false;
    }
}

void BatteryMonitor::monitorGpioValues()
{
    RCLCPP_INFO(this->get_logger(), "GPIO monitoring thread started");

    while (keep_monitoring_.load()) {
        auto current_time = std::chrono::steady_clock::now();

        // Read auto charging GPIO
        bool auto_gpio_value = readGpioValue(auto_charging_gpio_fd_);
        bool auto_is_charging = (auto_gpio_value == (auto_charging_trigger_value_ == 1));

        // Check if auto charging state changed
        if (auto_is_charging != auto_charging_last_stable_state_) {
            // State changed, update timestamp and stable state
            auto_charging_last_change_time_ = current_time;
            auto_charging_last_stable_state_ = auto_is_charging;
        } else {
            // State is stable, check if debounce time has passed
            auto time_since_change = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - auto_charging_last_change_time_).count();

            if (time_since_change >= gpio_debounce_time_ms_) {
                // Debounce time passed, update stable state if different from current
                if (auto_is_charging != is_charging_) {
                    RCLCPP_INFO(this->get_logger(), "Auto charging status changed: %s",
                              auto_is_charging ? "CHARGING" : "NOT CHARGING");

                    is_charging_ = auto_is_charging;
                }
            }
        }

        // Read manual charging GPIO
        bool manual_gpio_value = readGpioValue(manual_charging_gpio_fd_);
        bool manual_is_charging = (manual_gpio_value == (manual_charging_trigger_value_ == 1));

        // Check if manual charging state changed
        if (manual_is_charging != manual_charging_last_stable_state_) {
            // State changed, update timestamp and stable state
            manual_charging_last_change_time_ = current_time;
            manual_charging_last_stable_state_ = manual_is_charging;
        } else {
            // State is stable, check if debounce time has passed
            auto time_since_change = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - manual_charging_last_change_time_).count();

            if (time_since_change >= gpio_debounce_time_ms_) {
                // Debounce time passed, update stable state if different from current
                if (manual_is_charging != is_manual_charging_.load()) {
                    RCLCPP_INFO(this->get_logger(), "Manual charging status changed: %s",
                              manual_is_charging ? "CHARGING" : "NOT CHARGING");

                    is_manual_charging_.store(manual_is_charging);
                }
            }
        }

        // Sleep for the check period
        std::this_thread::sleep_for(std::chrono::milliseconds(gpio_check_period_ms_));
    }

    RCLCPP_INFO(this->get_logger(), "GPIO monitoring thread exiting");
}

void BatteryMonitor::monitorCanFrames()
{
    struct can_frame frame;
    struct timeval timeout;
    fd_set readSet;

    RCLCPP_INFO(this->get_logger(), "SocketCAN receive thread started");

    while (running_) {
        if (socket_fd_ < 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }

        // Set up select timeout (100 ms)
        timeout.tv_sec = 0;
        timeout.tv_usec = 100000;

        FD_ZERO(&readSet);
        FD_SET(socket_fd_, &readSet);

        // Wait for data or timeout
        int ret = select(socket_fd_ + 1, &readSet, NULL, NULL, &timeout);

        if (ret < 0) {
            if (errno != EINTR) {
                RCLCPP_ERROR(this->get_logger(), "Error in select: %s", strerror(errno));
            }
            continue;
        } else if (ret == 0) {
            // Timeout, continue
            continue;
        }

        // Check if data is available on the socket
        if (FD_ISSET(socket_fd_, &readSet)) {
            ssize_t nbytes = read(socket_fd_, &frame, sizeof(struct can_frame));

            if (nbytes < 0) {
                if (errno != EAGAIN && errno != EWOULDBLOCK) {
                    RCLCPP_ERROR(this->get_logger(), "Error reading CAN frame: %s", strerror(errno));
                }
                continue;
            }

            if (nbytes != sizeof(struct can_frame)) {
                RCLCPP_WARN(this->get_logger(), "Incomplete CAN frame read");
                continue;
            }

            // Process the CAN frame
            processCanFrame(frame);
        }
    }

    RCLCPP_INFO(this->get_logger(), "SocketCAN receive thread exiting");
}

void BatteryMonitor::processCanFrame(const struct can_frame& frame)
{
    // Extract CAN ID without flags
    uint32_t can_id = frame.can_id & CAN_EFF_MASK;

    if (can_id == can_id_battery_info_) {
        // Process battery info message (0x100)
        // byte0-1: voltage (10mV)
        // byte2-3: current (10mA)
        // byte4-5: remaining capacity (10mAh)
        voltage_ = convertToDouble(frame.data[0], frame.data[1], 0.01);  // Convert to V
        current_ = convertToDouble(frame.data[2], frame.data[3], 0.01, true);  // Convert to A
        //charge_ = convertToDouble(frame.data[4], frame.data[5], 0.01);   // Convert to Ah
    }
    else if (can_id == can_id_battery_status_) {
        // Process battery status message (0x101)
        // byte0-1: full capacity (10mAh)
        // byte2-3: cycle count
        // byte4-5: percentage
        capacity_ = convertToDouble(frame.data[0], frame.data[1], 0.01);  // Convert to Ah
        cycle_count_ = (frame.data[2] << 8) | frame.data[3];
        percentage_ = convertToDouble(frame.data[4], frame.data[5], 1.0);  // Already in percentage
    }

    RCLCPP_DEBUG(this->get_logger(), "Battery info: voltage=%.3fV, current=%.3fA, capacity=%.3fAh, cycle_count=%d, percentage=%.1f%%",
                voltage_, current_, capacity_, cycle_count_, percentage_);

}

double BatteryMonitor::convertToDouble(uint8_t high_byte, uint8_t low_byte, double scale, bool is_signed)
{
    uint16_t value = (high_byte << 8) | low_byte;
    if (is_signed) {
        int16_t signed_value = static_cast<int16_t>(value);
        return static_cast<double>(signed_value) * scale;
    } else {
        return static_cast<double>(value) * scale;
    }
}

void BatteryMonitor::publishBatteryState()
{
    auto msg = std::make_unique<sensor_msgs::msg::BatteryState>();
    
    msg->header.stamp = this->now();
    msg->header.frame_id = "battery_link";
    
    // Set battery state fields
    msg->voltage = voltage_;
    msg->current = current_;
    msg->charge = (current_ > 0.0 ? current_ : 0.0);
    msg->capacity = capacity_;
    msg->design_capacity = capacity_;  // Use current capacity as design capacity
    msg->percentage = percentage_;
    msg->power_supply_status = (is_charging_ || is_manual_charging_ || current_ > 0.0) ? 
        sensor_msgs::msg::BatteryState::POWER_SUPPLY_STATUS_CHARGING :
        sensor_msgs::msg::BatteryState::POWER_SUPPLY_STATUS_DISCHARGING;
    msg->power_supply_health = sensor_msgs::msg::BatteryState::POWER_SUPPLY_HEALTH_GOOD;
    msg->power_supply_technology = sensor_msgs::msg::BatteryState::POWER_SUPPLY_TECHNOLOGY_LION;
    msg->present = true;
    
    // Set cell voltages (if available)
    msg->cell_voltage.push_back(NAN);  // Single cell for now
    
    // Set cell temperatures (if available)
    msg->cell_temperature.push_back(NAN);  // Default temperature
    
    battery_state_pub_->publish(std::move(msg));
}

void BatteryMonitor::publishBatteryStatus()
{
    auto msg = std::make_unique<sl_vcu_all::msg::BatteryStatus>();
    
    msg->header.stamp = this->now();
    msg->header.frame_id = "battery_link";
    
    // Set battery states
    msg->is_auto_charging = is_charging_;
    msg->is_manual_charging = is_manual_charging_;
    msg->is_discharging = (!is_charging_ && !is_manual_charging_ && current_ < 0.1);
    msg->remaining_percent = percentage_;
    
    battery_status_pub_->publish(std::move(msg));
}

void BatteryMonitor::requestBatteryStatus()
{
    if (!is_socket_initialized_ || socket_fd_ < 0) {
        return;
    }

    // Check timing interval
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(now - last_send_time_).count();

    if (elapsed < min_send_interval_ms_ * 1000) {
        // Too soon, wait for the remaining time
        auto wait_time = min_send_interval_ms_ * 1000 - elapsed;
        std::this_thread::sleep_for(std::chrono::microseconds(wait_time));
    }

    // Send request for battery info (0x100)
    struct can_frame frame;
    frame.can_id = can_id_battery_info_;
    frame.can_dlc = 8;
    std::memset(frame.data, 0, 8);  // Send all zeros as request

    if (write(socket_fd_, &frame, sizeof(struct can_frame)) != sizeof(struct can_frame)) {
        RCLCPP_ERROR(this->get_logger(), "Error sending battery info request: %s", strerror(errno));
    } else {
        last_send_time_ = std::chrono::steady_clock::now();
        RCLCPP_DEBUG(this->get_logger(), "Sent battery info request");
    }

    // Wait for minimum interval
    std::this_thread::sleep_for(std::chrono::milliseconds(min_send_interval_ms_));

    // Send request for battery status (0x101)
    frame.can_id = can_id_battery_status_;
    std::memset(frame.data, 0, 8);  // Send all zeros as request

    if (write(socket_fd_, &frame, sizeof(struct can_frame)) != sizeof(struct can_frame)) {
        RCLCPP_ERROR(this->get_logger(), "Error sending battery status request: %s", strerror(errno));
    } else {
        last_send_time_ = std::chrono::steady_clock::now();
        RCLCPP_DEBUG(this->get_logger(), "Sent battery status request");
    }
}

}  // namespace sl_vcu_all

// Main entry point
int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<sl_vcu_all::BatteryMonitor>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
} 