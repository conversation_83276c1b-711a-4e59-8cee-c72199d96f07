#include "sl_vcu_all/bumper_sensor.hpp"
#include <iostream>
#include <fstream>
#include <string>
#include <chrono>
#include <cstring>
#include <system_error>

using namespace std::chrono_literals;

namespace sl_vcu_all
{

BumperSensor::BumperSensor(const rclcpp::NodeOptions & options)
: Node("bumper_sensor", options),
    front_bumper_gpio_fd_(-1),
    back_bumper_gpio_fd_(-1),
    front_bumper_triggered_(false),
    back_bumper_triggered_(false),
    bumper_status_(0),
    keep_monitoring_(true),
    front_last_stable_state_(false),
    back_last_stable_state_(false),
    bumper_disabled_(false)
{
    // Initialize parameters
    initParameters();

    RCLCPP_INFO(this->get_logger(), "Initializing Bumper Sensor");

    // Create publisher
    bumper_state_pub_ = this->create_publisher<sl_vcu_all::msg::BumperState>(bumper_topic_, 10);

    // Create subscriber for disable topic
    disable_sub_ = this->create_subscription<std_msgs::msg::Bool>(
        "/sensors/bumper/disable", 10,
        std::bind(&BumperSensor::disableCallback, this, std::placeholders::_1));

    // Initialize GPIO files
    bool gpio_ok = initGpioFiles();
    if (!gpio_ok) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize GPIO files, bumpers may not function");
    }

    // Initialize debounce timestamps
    front_last_change_time_ = std::chrono::steady_clock::now();
    back_last_change_time_ = std::chrono::steady_clock::now();
    
    // Initialize disable timestamp
    last_disable_update_time_ = std::chrono::steady_clock::now();

    // Start GPIO monitoring thread
    gpio_thread_ = std::thread(&BumperSensor::monitorGpioValues, this);

    // Create timer for publishing state
    publish_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(publish_rate_ms_),
        std::bind(&BumperSensor::publishBumperState, this));

    RCLCPP_INFO(this->get_logger(), "Bumper Sensor initialized");
}

BumperSensor::~BumperSensor()
{
    // Stop monitoring thread
    keep_monitoring_ = false;

    if (gpio_thread_.joinable()) {
        gpio_thread_.join();
    }

    // Close GPIO file descriptors
    if (front_bumper_gpio_fd_ >= 0) {
        close(front_bumper_gpio_fd_);
    }
    if (back_bumper_gpio_fd_ >= 0) {
        close(back_bumper_gpio_fd_);
    }
}

void BumperSensor::initParameters()
{
    // GPIO parameters
    this->declare_parameter("front_bumper_gpio_path", "/sys/class/gpio/gpio156/value");
    this->declare_parameter("back_bumper_gpio_path", "/sys/class/gpio/gpio157/value");
    this->declare_parameter("front_bumper_trigger_value", 0);
    this->declare_parameter("back_bumper_trigger_value", 0);
    this->declare_parameter("gpio_check_period_ms", 10);
    this->declare_parameter("gpio_debounce_time_ms", 10);
    this->declare_parameter("publish_rate_ms", 20);
    this->declare_parameter("bumper_topic", "bumper_state");
    this->declare_parameter("disable_timeout_ms", 1000);

    // Get parameter values
    front_bumper_gpio_path_ = this->get_parameter("front_bumper_gpio_path").as_string();
    back_bumper_gpio_path_ = this->get_parameter("back_bumper_gpio_path").as_string();
    front_bumper_trigger_value_ = this->get_parameter("front_bumper_trigger_value").as_int();
    back_bumper_trigger_value_ = this->get_parameter("back_bumper_trigger_value").as_int();
    gpio_check_period_ms_ = this->get_parameter("gpio_check_period_ms").as_int();
    gpio_debounce_time_ms_ = this->get_parameter("gpio_debounce_time_ms").as_int();
    publish_rate_ms_ = this->get_parameter("publish_rate_ms").as_int();
    bumper_topic_ = this->get_parameter("bumper_topic").as_string();
    disable_timeout_ms_ = this->get_parameter("disable_timeout_ms").as_int();

    RCLCPP_INFO(this->get_logger(), "Parameters initialized:");
    RCLCPP_INFO(this->get_logger(), "  Front bumper GPIO path: %s", front_bumper_gpio_path_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Back bumper GPIO path: %s", back_bumper_gpio_path_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Front bumper trigger value: %d", front_bumper_trigger_value_);
    RCLCPP_INFO(this->get_logger(), "  Back bumper trigger value: %d", back_bumper_trigger_value_);
    RCLCPP_INFO(this->get_logger(), "  GPIO check period: %d ms", gpio_check_period_ms_);
    RCLCPP_INFO(this->get_logger(), "  GPIO debounce time: %d ms", gpio_debounce_time_ms_);
    RCLCPP_INFO(this->get_logger(), "  Publish rate: %d ms", publish_rate_ms_);
    RCLCPP_INFO(this->get_logger(), "  Bumper topic: %s", bumper_topic_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Disable timeout: %d ms", disable_timeout_ms_);
}

bool BumperSensor::initGpioFiles()
{
    // Open front bumper GPIO file
    front_bumper_gpio_fd_ = open(front_bumper_gpio_path_.c_str(), O_RDONLY);
    if (front_bumper_gpio_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to open front bumper GPIO file: %s", front_bumper_gpio_path_.c_str());
        return false;
    }

    // Open back bumper GPIO file
    back_bumper_gpio_fd_ = open(back_bumper_gpio_path_.c_str(), O_RDONLY);
    if (back_bumper_gpio_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to open back bumper GPIO file: %s", back_bumper_gpio_path_.c_str());
        close(front_bumper_gpio_fd_);
        front_bumper_gpio_fd_ = -1;
        return false;
    }

    RCLCPP_INFO(this->get_logger(), "Successfully opened GPIO files");
    return true;
}

bool BumperSensor::readGpioValue(int gpio_fd)
{
    if (gpio_fd < 0) {
        return false;
    }

    // Seek to beginning of file
    if (lseek(gpio_fd, 0, SEEK_SET) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to seek GPIO file");
        return false;
    }

    char buffer[8];
    ssize_t bytes_read = read(gpio_fd, buffer, sizeof(buffer) - 1);
    if (bytes_read <= 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to read GPIO value");
        return false;
    }

    buffer[bytes_read] = '\0';

    try {
        int value = std::stoi(buffer);
        return (value == 1);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Failed to parse GPIO value '%s': %s", buffer, e.what());
        return false;
    }
}

void BumperSensor::monitorGpioValues()
{
    RCLCPP_INFO(this->get_logger(), "GPIO monitoring thread started");

    while (keep_monitoring_.load()) {
        auto current_time = std::chrono::steady_clock::now();

        // Read front bumper GPIO
        bool front_gpio_value = readGpioValue(front_bumper_gpio_fd_);
        bool front_is_triggered = (front_gpio_value == (front_bumper_trigger_value_ == 1));

        // Check if front bumper state changed
        if (front_is_triggered != front_last_stable_state_) {
            // State changed, update timestamp
            front_last_change_time_ = current_time;
            front_last_stable_state_ = front_is_triggered;
        } else {
            // State is stable, check if debounce time has passed
            auto time_since_change = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - front_last_change_time_).count();

            if (time_since_change >= gpio_debounce_time_ms_) {
                // Debounce time passed, update stable state if different from current
                if (front_is_triggered != front_bumper_triggered_.load()) {
                    RCLCPP_INFO(this->get_logger(), "Front bumper %s",
                              front_is_triggered ? "TRIGGERED" : "RELEASED");

                    front_bumper_triggered_.store(front_is_triggered);

                    // Update bumper status
                    if (front_is_triggered) {
                        bumper_status_.fetch_or(0x01);
                    } else {
                        bumper_status_.fetch_and(~0x01);
                    }
                }
                //front_last_stable_state_ = front_is_triggered;
            }
        }

        // Read back bumper GPIO
        bool back_gpio_value = readGpioValue(back_bumper_gpio_fd_);
        bool back_is_triggered = (back_gpio_value == (back_bumper_trigger_value_ == 1));

        // Check if back bumper state changed
        if (back_is_triggered != back_last_stable_state_) {
            // State changed, update timestamp
            back_last_change_time_ = current_time;
            back_last_stable_state_ = back_is_triggered;
        } else {
            // State is stable, check if debounce time has passed
            auto time_since_change = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - back_last_change_time_).count();

            if (time_since_change >= gpio_debounce_time_ms_) {
                // Debounce time passed, update stable state if different from current
                if (back_is_triggered != back_bumper_triggered_.load()) {
                    RCLCPP_INFO(this->get_logger(), "Back bumper %s",
                              back_is_triggered ? "TRIGGERED" : "RELEASED");

                    back_bumper_triggered_.store(back_is_triggered);

                    // Update bumper status
                    if (back_is_triggered) {
                        bumper_status_.fetch_or(0x02);
                    } else {
                        bumper_status_.fetch_and(~0x02);
                    }
                }
                //back_last_stable_state_ = back_is_triggered;
            }
        }

        // Sleep for the check period
        std::this_thread::sleep_for(std::chrono::milliseconds(gpio_check_period_ms_));
    }

    RCLCPP_INFO(this->get_logger(), "GPIO monitoring thread exiting");
}

void BumperSensor::publishBumperState()
{
    auto msg = std::make_unique<sl_vcu_all::msg::BumperState>();
    
    msg->header.stamp = this->now();
    msg->header.frame_id = "bumper_link";
    
    // Check if bumper is disabled and timeout hasn't expired
    bool disable_active = false;
    if (bumper_disabled_.load()) {
        auto current_time = std::chrono::steady_clock::now();
        auto time_since_disable = std::chrono::duration_cast<std::chrono::milliseconds>(
            current_time - last_disable_update_time_).count();
        
        if (time_since_disable < disable_timeout_ms_) {
            disable_active = true;
            bumper_disabled_.store(false);
            RCLCPP_DEBUG(this->get_logger(), "Bumper disable timeout expired, enabling bumper");
        }
    }
    
    if (disable_active) {
        // Set all bumper states to false when disabled
        msg->front_bumper_triggered = false;
        msg->back_bumper_triggered = false;
        msg->bumper_status = 0;
    } else {
        // Use normal bumper states
        msg->front_bumper_triggered = front_bumper_triggered_.load();
        msg->back_bumper_triggered = back_bumper_triggered_.load();
        msg->bumper_status = bumper_status_.load();
    }
    
    bumper_state_pub_->publish(std::move(msg));
}

void BumperSensor::disableCallback(const std_msgs::msg::Bool::SharedPtr msg)
{
    bumper_disabled_.store(msg->data);
    last_disable_update_time_ = std::chrono::steady_clock::now();
    
    RCLCPP_DEBUG(this->get_logger(), "Bumper disable status changed to: %s", 
                msg->data ? "DISABLED" : "ENABLED");
}

} // namespace sl_vcu_all

// Main entry point
int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<sl_vcu_all::BumperSensor>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
} 